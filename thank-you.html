<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="UTF-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<title>Payment Complete - SPCA Fiji Donations</title>
		<link rel="stylesheet" href="styles.css" />
		<style>
			/* Additional styles for thank you page */
			.thank-you-container {
				max-width: 800px;
				margin: 0 auto;
				padding: 20px;
			}

			.thank-you-card {
				background: white;
				padding: 40px;
				border-radius: 15px;
				box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
				text-align: center;
				margin-bottom: 30px;
			}

			.thank-you-icon {
				font-size: 4rem;
				margin-bottom: 20px;
			}

			.success-icon {
				color: #4caf50;
			}

			.error-icon {
				color: #f44336;
			}

			.warning-icon {
				color: #ff9800;
			}

			.thank-you-title {
				font-size: 2rem;
				margin-bottom: 15px;
				color: #333;
			}

			.thank-you-message {
				font-size: 1.1rem;
				color: #666;
				margin-bottom: 30px;
				line-height: 1.6;
			}

			.transaction-details {
				background: #f8f9fa;
				padding: 20px;
				border-radius: 8px;
				margin-bottom: 30px;
				text-align: left;
			}

			.transaction-details h3 {
				margin-bottom: 15px;
				color: #333;
			}

			.detail-row {
				display: flex;
				justify-content: space-between;
				margin-bottom: 10px;
				padding: 8px 0;
				border-bottom: 1px solid #eee;
			}

			.detail-row:last-child {
				border-bottom: none;
			}

			.detail-label {
				font-weight: 600;
				color: #555;
			}

			.detail-value {
				color: #333;
			}

			.return-button {
				display: inline-block;
				padding: 15px 30px;
				background: #000;
				color: white;
				text-decoration: none;
				border-radius: 10px;
				font-size: 1.1rem;
				font-weight: 600;
				transition: all 0.3s ease;
				margin-right: 15px;
			}

			.return-button:hover {
				background: #333;
				transform: translateY(-2px);
				box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
			}

			.debug-toggle {
				background: #666;
				font-size: 0.9rem;
				padding: 10px 20px;
			}

			.debug-toggle:hover {
				background: #555;
			}

			.hidden {
				display: none;
			}

			@media (max-width: 768px) {
				.thank-you-card {
					padding: 30px 20px;
				}

				.thank-you-title {
					font-size: 1.5rem;
				}

				.return-button {
					display: block;
					margin-bottom: 10px;
					margin-right: 0;
					text-align: center;
				}
			}
		</style>
	</head>
	<body>
		<div class="container">
			<header>
				<h1>SPCA Fiji Islands</h1>
				<h2>Payment Complete</h2>
			</header>

			<div class="thank-you-container">
				<div class="thank-you-card">
					<div id="thankYouIcon" class="thank-you-icon">⏳</div>

					<h2 id="thankYouTitle" class="thank-you-title">Processing Payment Response...</h2>

					<p id="thankYouMessage" class="thank-you-message">
						Please wait while we process your payment response.
					</p>

					<div id="transactionDetails" class="transaction-details hidden">
						<h3>Transaction Details</h3>
						<div id="detailsContent">
							<!-- Transaction details will be populated here -->
						</div>
					</div>

					<div class="button-group">
						<a href="index.html" class="return-button">Return to Homepage</a>
						<button type="button" id="toggleDebug" class="return-button debug-toggle">
							Show Debug Info
						</button>
					</div>
				</div>

				<div id="debugSection" class="debug-section hidden">
					<h3>Debug Console</h3>
					<div id="debugConsole" class="debug-console">
						<div class="debug-message">
							<span class="timestamp">[Ready]</span>
							<span class="message">Payment response processing...</span>
						</div>
					</div>
					<button type="button" id="clearDebug" class="clear-button">Clear Console</button>
				</div>
			</div>
		</div>

		<!-- JavaScript modules -->
		<script src="js/config.js"></script>
		<script src="js/error-handler.js"></script>
		<script src="js/hmac-service.js"></script>
		<script src="js/payment-service.js"></script>
		<script src="js/debug-service.js"></script>
		<script>
			/**
			 * Thank You Page Controller
			 * Handles payment response processing and display
			 */
			class ThankYouPageController {
				constructor() {
					this.responseData = null;
					this.paymentStatus = 'UNKNOWN';
				}

				async init() {
					try {
						// Initialize debug service
						const debugConsole = document.getElementById('debugConsole');
						debugService.init(debugConsole);

						// Initialize other services
						errorHandler.setDebugService(debugService);
						hmacService.setDebugService(debugService);
						paymentService.init(debugService, hmacService, errorHandler);

						// Setup event listeners
						this.setupEventListeners();

						// Process payment response
						await this.processPaymentResponse();
					} catch (error) {
						debugService.logError(error, 'Thank you page initialization failed');
						this.showError('Failed to initialize payment response processing');
					}
				}

				setupEventListeners() {
					const toggleDebug = document.getElementById('toggleDebug');
					const clearDebug = document.getElementById('clearDebug');
					const debugSection = document.getElementById('debugSection');

					toggleDebug.addEventListener('click', () => {
						debugSection.classList.toggle('hidden');
						toggleDebug.textContent = debugSection.classList.contains('hidden')
							? 'Show Debug Info'
							: 'Hide Debug Info';
					});

					clearDebug.addEventListener('click', () => {
						debugService.clear();
					});
				}

				async processPaymentResponse() {
					debugService.log('info', 'Processing payment response from BSP IPG');

					try {
						// Get response data from URL parameters or form data
						const urlParams = new URLSearchParams(window.location.search);
						const responseData = {};

						// Extract all BSP IPG response parameters
						for (const [key, value] of urlParams) {
							responseData[key] = value;
						}

						if (Object.keys(responseData).length === 0) {
							debugService.logWarning('No response parameters found in URL');
							this.showWarning('No payment response data received');
							return;
						}

						this.responseData = responseData;

						// Process the response
						const processedResponse = paymentService.handlePaymentResponse(responseData);
						this.paymentStatus = paymentService.getPaymentStatus(processedResponse);

						// Display results
						this.displayPaymentResult();
					} catch (error) {
						debugService.logError(error, 'Payment response processing failed');
						this.showError('Failed to process payment response');
					}
				}

				displayPaymentResult() {
					const iconElement = document.getElementById('thankYouIcon');
					const titleElement = document.getElementById('thankYouTitle');
					const messageElement = document.getElementById('thankYouMessage');
					const detailsElement = document.getElementById('transactionDetails');

					if (this.paymentStatus === 'SUCCESS') {
						iconElement.innerHTML = '✅';
						iconElement.className = 'thank-you-icon success-icon';
						titleElement.textContent = 'Payment Successful!';
						messageElement.textContent =
							'Thank you for your donation to SPCA Fiji Islands. Your payment has been processed successfully.';
					} else if (this.paymentStatus === 'FAILED') {
						iconElement.innerHTML = '❌';
						iconElement.className = 'thank-you-icon error-icon';
						titleElement.textContent = 'Payment Failed';
						messageElement.textContent =
							'Unfortunately, your payment could not be processed. Please try again or contact support.';
					} else {
						iconElement.innerHTML = '⚠️';
						iconElement.className = 'thank-you-icon warning-icon';
						titleElement.textContent = 'Payment Status Unknown';
						messageElement.textContent =
							'We received a response but could not determine the payment status. Please check with support.';
					}

					// Show transaction details
					this.displayTransactionDetails();
					detailsElement.classList.remove('hidden');
				}

				displayTransactionDetails() {
					const detailsContent = document.getElementById('detailsContent');
					const details = [
						{ label: 'Order Number', value: this.responseData.nar_orderNo || 'N/A' },
						{ label: 'Transaction ID', value: this.responseData.nar_narTxnId || 'N/A' },
						{
							label: 'Amount',
							value: this.responseData.nar_txnAmount ? `FJD ${this.responseData.nar_txnAmount}` : 'N/A',
						},
						{ label: 'Status', value: this.responseData.nar_remarks || 'N/A' },
						{ label: 'Auth Code', value: this.responseData.nar_debitAuthCode || 'N/A' },
						{ label: 'Card Type', value: this.responseData.nar_cardType || 'N/A' },
						{ label: 'Transaction Time', value: this.responseData.nar_narTxnTime || 'N/A' },
					];

					detailsContent.innerHTML = details
						.map(
							(detail) => `
                    <div class="detail-row">
                        <span class="detail-label">${detail.label}:</span>
                        <span class="detail-value">${detail.value}</span>
                    </div>
                `
						)
						.join('');
				}

				showError(message) {
					const iconElement = document.getElementById('thankYouIcon');
					const titleElement = document.getElementById('thankYouTitle');
					const messageElement = document.getElementById('thankYouMessage');

					iconElement.innerHTML = '❌';
					iconElement.className = 'thank-you-icon error-icon';
					titleElement.textContent = 'Error';
					messageElement.textContent = message;
				}

				showWarning(message) {
					const iconElement = document.getElementById('thankYouIcon');
					const titleElement = document.getElementById('thankYouTitle');
					const messageElement = document.getElementById('thankYouMessage');

					iconElement.innerHTML = '⚠️';
					iconElement.className = 'thank-you-icon warning-icon';
					titleElement.textContent = 'Warning';
					messageElement.textContent = message;
				}
			}

			// Initialize when DOM is loaded
			document.addEventListener('DOMContentLoaded', async () => {
				const controller = new ThankYouPageController();
				await controller.init();
			});
		</script>
	</body>
</html>
