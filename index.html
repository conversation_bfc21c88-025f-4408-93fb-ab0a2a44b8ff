<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="UTF-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<meta name="referrer" content="unsafe-url" />
		<title>SPCA Fiji Donations - BSP IPG Payment Testing</title>
		<link rel="stylesheet" href="styles.css" />
	</head>
	<body>
		<div class="container">
			<header>
				<h1>SPCA Fiji Islands</h1>
				<h2>Donation Payment Testing</h2>
				<p>BSP IPG Integration Test Environment</p>
			</header>

			<main>
				<div class="payment-section">
					<div class="amount-config">
						<label for="amount">Donation Amount (FJD):</label>
						<input type="number" id="amount" value="1.00" min="0.01" step="0.01" />
					</div>

					<button type="button" id="payButton" class="pay-button">Donate Now via BSP IPG</button>

					<div class="merchant-info">
						<p><strong>Merchant:</strong> SPCA Fiji Islands</p>
						<p><strong>Currency:</strong> FJD (242)</p>
						<p><strong>Environment:</strong> Production</p>
					</div>

					<div class="test-tools">
						<a href="test-hmac.html" class="test-button">🔧 HMAC SHA-256 Testing</a>
					</div>
				</div>

				<div class="debug-section">
					<h3>Debug Console</h3>
					<div id="debugConsole" class="debug-console">
						<div class="debug-message">
							<span class="timestamp">[Ready]</span>
							<span class="message">BSP IPG Payment Testing Application initialized</span>
						</div>
					</div>
					<button type="button" id="clearDebug" class="clear-button">Clear Console</button>
				</div>
			</main>

			<!-- BSP IPG Payment Form -->
			<form id="bspPaymentForm" method="post" class="hidden-form">
				<input type="hidden" name="nar_msgType" value="AR" />
				<input type="hidden" name="nar_merTxnTime" id="nar_merTxnTime" />
				<input type="hidden" name="nar_orderNo" id="nar_orderNo" />
				<input type="hidden" name="nar_merId" id="nar_merId" />
				<input type="hidden" name="nar_merBankCode" value="01" />
				<input type="hidden" name="nar_txnCurrency" value="242" />
				<input type="hidden" name="nar_txnAmount" id="nar_txnAmount" />
				<input type="hidden" name="nar_remitterEmail" value="" />
				<input type="hidden" name="nar_remitterMobile" value="" />
				<input type="hidden" name="nar_cardType" value="EX" />
				<input type="hidden" name="nar_checkSum" id="nar_checkSum" />
				<input type="hidden" name="nar_paymentDesc" id="nar_paymentDesc" />
				<input type="hidden" name="nar_version" value="1.0" />
				<input type="hidden" name="nar_returnUrl" id="nar_returnUrl" />
				<input type="hidden" name="nar_mcccode" id="nar_mcccode" />
				<input type="hidden" name="nar_Secure" value="MERSECURE" />
			</form>
		</div>

		<!-- JavaScript modules -->
		<script src="js/config.js"></script>
		<script src="js/error-handler.js"></script>
		<script src="js/hmac-service.js"></script>
		<script src="js/payment-service.js"></script>
		<script src="js/debug-service.js"></script>
		<script src="js/app.js"></script>
	</body>
</html>
