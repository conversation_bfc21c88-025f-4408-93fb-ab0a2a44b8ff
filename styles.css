/* BSP IPG Payment Testing Application Styles */

* {
	margin: 0;
	padding: 0;
	box-sizing: border-box;
}

body {
	font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
	background: black;
	min-height: 100vh;
	color: #333;
}

.container {
	max-width: 1200px;
	margin: 0 auto;
	padding: 20px;
}

/* Header Styles */
header {
	text-align: center;
	margin-bottom: 40px;
	color: white;
}

header h1 {
	font-size: 2.5rem;
	margin-bottom: 10px;
	text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

header h2 {
	font-size: 1.5rem;
	margin-bottom: 10px;
	opacity: 0.9;
}

header p {
	font-size: 1rem;
	opacity: 0.8;
}

/* Main Content */
main {
	max-width: 800px;
	margin: 0 auto;
}

/* Payment Section */
.payment-section {
	background: white;
	padding: 30px;
	border-radius: 15px;
	box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
	margin-bottom: 30px;
	text-align: center;
}

.amount-config {
	margin-bottom: 25px;
}

.amount-config label {
	display: block;
	margin-bottom: 8px;
	font-weight: 600;
	color: #555;
}

.amount-config input {
	width: 100%;
	padding: 12px;
	border: 2px solid #ddd;
	border-radius: 8px;
	font-size: 1.1rem;
	transition: border-color 0.3s ease;
}

.amount-config input:focus {
	outline: none;
	border-color: #667eea;
}

/* Payment Button */
.pay-button {
	width: 100%;
	padding: 15px 30px;
	background: #000;
	color: white;
	border: none;
	border-radius: 10px;
	font-size: 1.2rem;
	font-weight: 600;
	cursor: pointer;
	transition: all 0.3s ease;
	margin-bottom: 25px;
}

.pay-button:hover {
	background: #333;
	transform: translateY(-2px);
	box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.pay-button:disabled {
	background: #ccc;
	cursor: not-allowed;
	transform: none;
	box-shadow: none;
}

/* Merchant Info */
.merchant-info {
	background: #f8f9fa;
	padding: 20px;
	border-radius: 8px;
	border-left: 4px solid #667eea;
	margin-bottom: 20px;
}

/* Test Tools Section */
.test-tools {
	text-align: center;
}

.test-button {
	display: inline-block;
	padding: 12px 24px;
	background: #666;
	color: white;
	text-decoration: none;
	border-radius: 8px;
	font-size: 0.95rem;
	font-weight: 600;
	transition: all 0.3s ease;
	border: none;
	cursor: pointer;
}

.test-button:hover {
	background: #555;
	transform: translateY(-2px);
	box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.merchant-info p {
	margin-bottom: 8px;
	font-size: 0.95rem;
}

.merchant-info p:last-child {
	margin-bottom: 0;
}

/* Debug Section */
.debug-section {
	background: white;
	padding: 30px;
	border-radius: 15px;
	box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.debug-section h3 {
	margin-bottom: 20px;
	color: #333;
	font-size: 1.3rem;
}

/* Debug Console */
.debug-console {
	background: #1e1e1e;
	color: #fff;
	padding: 20px;
	border-radius: 8px;
	height: 400px;
	overflow-y: auto;
	font-family: 'Courier New', monospace;
	font-size: 0.9rem;
	line-height: 1.4;
	margin-bottom: 15px;
}

.debug-message {
	margin-bottom: 8px;
	display: flex;
	align-items: flex-start;
	gap: 10px;
}

.timestamp {
	color: #888;
	font-size: 0.85rem;
	white-space: nowrap;
	min-width: 80px;
}

.message {
	flex: 1;
	word-break: break-word;
}

.message.info {
	color: #61dafb;
}

.message.success {
	color: #4caf50;
}

.message.warning {
	color: #ff9800;
}

.message.error {
	color: #f44336;
}

/* Clear Button */
.clear-button {
	padding: 8px 16px;
	background: #666;
	color: white;
	border: none;
	border-radius: 5px;
	cursor: pointer;
	font-size: 0.9rem;
	transition: background 0.3s ease;
}

.clear-button:hover {
	background: #555;
}

/* Responsive Design */
@media (max-width: 768px) {
	header h1 {
		font-size: 2rem;
	}

	header h2 {
		font-size: 1.2rem;
	}

	.payment-section,
	.debug-section {
		padding: 20px;
	}

	.debug-console {
		height: 300px;
	}
}

/* Scrollbar Styling for Debug Console */
.debug-console::-webkit-scrollbar {
	width: 8px;
}

.debug-console::-webkit-scrollbar-track {
	background: #2d2d2d;
	border-radius: 4px;
}

.debug-console::-webkit-scrollbar-thumb {
	background: #555;
	border-radius: 4px;
}

.debug-console::-webkit-scrollbar-thumb:hover {
	background: #777;
}

/* Hidden form */
.hidden-form {
	display: none;
}
