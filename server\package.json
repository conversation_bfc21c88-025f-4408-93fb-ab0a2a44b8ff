{"name": "bsp-ipg-payment-server", "version": "1.0.0", "description": "Secure server-side BSP IPG payment processing for SPCA Fiji", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "dotenv": "^16.3.1", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0"}, "keywords": ["bsp", "ipg", "payment", "fiji", "spca", "hmac", "security"], "author": "SPCA Fiji Islands", "license": "MIT"}