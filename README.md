# SPCA Fiji Donations - BSP IPG Integration

A secure donation platform for SPCA Fiji integrated with Bank of South Pacific's Internet Payment Gateway (BSP IPG).

## Features

- **BSP IPG Integration**: Secure payment processing using HMAC SHA-256
- **Environment Switching**: Support for UAT and Production environments
- **Responsive Design**: Mobile-friendly donation interface
- **Security**: Server-side HMAC calculation, no sensitive data in client code
- **Testing**: Built-in HMAC testing and validation tools

## Quick Start

### 1. Environment Setup

Copy the environment template:
```bash
cp .env.example server/.env
```

Fill in your BSP IPG credentials in `server/.env`:
```bash
BSP_HMAC_KEY=your_production_hmac_key
MERCHANT_ID=your_merchant_id
MCC_CODE=your_mcc_code
# ... other required values
```

### 2. Local Development

```bash
cd server
npm install
npm start
```

The application will be available at `http://localhost:3000`

### 3. Vercel Deployment

1. Connect your repository to Vercel
2. Configure environment variables in Project Settings
3. Deploy

## BSP IPG Configuration

The integration follows BSP IPG Merchant Integration Manual v2.54 specifications:

- **Protocol Constants**: Hardcoded as per BSP specification (AR, 01, 242, 1.0, MERSECURE, EX)
- **Configurable Values**: Environment variables for credentials and business settings
- **HMAC SHA-256**: Server-side calculation with proper source string construction
- **Field Exclusion**: `nar_mcccode` excluded from HMAC but included in POST

## File Structure

```
├── api/                    # Vercel serverless functions
├── js/                     # Client-side JavaScript
├── server/                 # Local development server
├── index.html              # Main donation page
├── test-hmac.html          # HMAC testing interface
└── thank-you.html          # Payment confirmation page
```

## Testing

Visit `/test-hmac.html` to:
- Test HMAC SHA-256 implementation
- Validate BSP IPG compliance
- Check environment variable configuration

## Security

- All sensitive credentials stored in environment variables
- HMAC calculations performed server-side only
- No production credentials in source code
- Proper CORS and security headers

## Support

For BSP IPG integration issues, refer to the BSP Merchant Integration Manual v2.54 or contact BSP support.
