<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="UTF-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<title>HMAC Test - SPCA Fiji Donations</title>
		<link rel="stylesheet" href="styles.css" />
		<style>
			/* Additional styles for test page */
			.test-controls {
				background: white;
				padding: 30px;
				border-radius: 15px;
				box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
				margin-bottom: 30px;
				text-align: center;
			}

			.test-button {
				display: inline-block;
				padding: 12px 24px;
				background: #000;
				color: white;
				border: none;
				border-radius: 8px;
				font-size: 1rem;
				font-weight: 600;
				cursor: pointer;
				transition: all 0.3s ease;
				margin: 5px;
			}

			.test-button:hover {
				background: #333;
				transform: translateY(-2px);
				box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
			}

			.test-button.secondary {
				background: #666;
			}

			.test-button.secondary:hover {
				background: #555;
			}

			.test-results {
				background: white;
				padding: 30px;
				border-radius: 15px;
				box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
			}

			.test-result {
				padding: 15px;
				margin: 15px 0;
				border-radius: 8px;
				border-left: 4px solid #ddd;
			}

			.test-result.success {
				background: #d4edda;
				color: #155724;
				border-left-color: #4caf50;
			}

			.test-result.error {
				background: #f8d7da;
				color: #721c24;
				border-left-color: #f44336;
			}

			.test-result.info {
				background: #d1ecf1;
				color: #0c5460;
				border-left-color: #61dafb;
			}

			.test-result pre {
				background: #f8f9fa;
				padding: 15px;
				border-radius: 6px;
				overflow-x: auto;
				font-size: 0.9rem;
				font-family: 'Courier New', monospace;
				margin: 10px 0;
			}

			.test-description {
				color: #666;
				margin-bottom: 30px;
				font-size: 1.1rem;
				line-height: 1.6;
			}

			.test-button-link {
				text-decoration: none;
			}

			.env-status {
				font-family: 'Courier New', monospace;
				font-size: 0.9rem;
			}

			.status-ok {
				color: #28a745;
			}

			.status-empty {
				color: #ffc107;
			}

			.status-missing {
				color: #dc3545;
			}
		</style>
	</head>
	<body>
		<div class="container">
			<header>
				<h1>SPCA Fiji Islands</h1>
				<h2>HMAC SHA-256 Testing</h2>
				<p>BSP IPG Integration Test Environment</p>
			</header>

			<main>
				<div class="test-controls">
					<p class="test-description">
						This page tests the HMAC SHA-256 implementation against BSP IPG manual specifications, verifies
						the production configuration, and checks environment variable setup.
					</p>

					<button type="button" class="test-button" onclick="runTests()">Run HMAC Tests</button>
					<button type="button" class="test-button" onclick="testCurrentConfig()">Test Current Config</button>
					<button type="button" class="test-button" onclick="testEnvironmentVars()">
						Test Environment Variables
					</button>
					<button type="button" class="test-button secondary" onclick="clearResults()">Clear Results</button>
					<a href="index.html" class="test-button secondary test-button-link">Back to Payment</a>
				</div>

				<div class="test-results">
					<h3>Test Results</h3>
					<div id="results">
						<div class="test-result info">
							<strong>Ready to run tests</strong><br />
							Click "Run HMAC Tests" to verify the implementation, "Test Current Config" to test with
							production data, or "Test Environment Variables" to check credential configuration.
						</div>
					</div>
				</div>
			</main>
		</div>

		<script src="js/config.js"></script>
		<script src="js/hmac-service.js"></script>
		<script>
			let results = document.getElementById('results');

			function log(message, type = 'info') {
				const div = document.createElement('div');
				div.className = `test-result ${type}`;
				div.innerHTML = message;
				results.appendChild(div);

				// Auto-scroll to bottom
				div.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
			}

			function clearResults() {
				results.innerHTML = `
					<div class="test-result info">
						<strong>Ready to run tests</strong><br>
						Click "Run HMAC Tests" to verify the implementation, "Test Current Config" to test with production data, or "Test Environment Variables" to check credential configuration.
					</div>
				`;
			}

			async function runTests() {
				clearResults();
				log('<h4>🔧 Running HMAC SHA-256 Tests</h4>', 'info');

				// Test 1: Basic HMAC functionality (using secure server API)
				try {
					const testMessage = 'Hello World';

					log('<strong>🔒 Testing HMAC via Secure Server API</strong>', 'info');
					const result = await hmacService.testHMAC(testMessage);

					log(
						`<strong>✅ Basic HMAC Test Passed</strong><br>
						Message: <code>"${testMessage}"</code><br>
						HMAC: <code>${result.checksum}</code><br>
						<em>Calculated securely on server</em>`,
						'success'
					);
				} catch (error) {
					log(`<strong>❌ Basic HMAC Test Failed</strong><br>Error: ${error.message}`, 'error');
				}

				// Test 2: Source string construction and nar_mcccode exclusion (using secure server API)
				try {
					// Ensure config is loaded to get configurable values
					await configService.ensureLoaded();

					const testData = {
						nar_cardType: 'EX',
						nar_merBankCode: '01',
						nar_merId: 'SERVER_MERCHANT_ID', // Server will use actual MERCHANT_ID from .env
						nar_merTxnTime: '**************',
						nar_msgType: 'AR',
						nar_orderNo: 'SPCA-**************-001',
						nar_paymentDesc: BSP_CONFIG.paymentDescription || 'SPCA Fiji Donation',
						nar_remitterEmail: '',
						nar_remitterMobile: '',
						nar_txnAmount: BSP_CONFIG.defaultAmount || '1.00',
						nar_txnCurrency: '242',
						nar_version: '1.0',
						nar_returnUrl: BSP_CONFIG.returnUrl || 'https://donations.spcafiji.com/thank-you',
						nar_mcccode: 'SERVER_MCC_CODE', // Server will use actual MCC_CODE from .env, excluded from HMAC
					};

					log('<strong>🔒 Testing Source String Construction via Secure Server API</strong>', 'info');
					const result = await hmacService.testHMAC(null, testData);

					// Verify BSP specification compliance
					const expectedOrder =
						'nar_cardType|nar_merBankCode|nar_merId|nar_merTxnTime|nar_msgType|nar_orderNo|nar_paymentDesc|nar_remitterEmail|nar_remitterMobile|nar_txnAmount|nar_txnCurrency|nar_version|nar_returnUrl';
					const actualFields = result.sourceString.split('|').length;
					const expectedFields = 13; // BSP specification: 13 fields in HMAC source string

					if (actualFields === expectedFields) {
						log(
							`<strong>✅ Source String Field Count Correct</strong><br>Fields: ${actualFields}/13 (BSP specification)`,
							'success'
						);
					} else {
						log(
							`<strong>❌ Source String Field Count Incorrect</strong><br>Expected: 13, Got: ${actualFields}`,
							'error'
						);
					}

					// Verify nar_mcccode exclusion from HMAC
					if (!result.sourceString.includes('TEST_MCC_CODE')) {
						log(
							`<strong>✅ nar_mcccode Correctly Excluded from HMAC</strong><br>nar_mcccode (TEST_MCC_CODE) not found in source string`,
							'success'
						);
					} else {
						log(
							`<strong>❌ nar_mcccode Incorrectly Included in HMAC</strong><br>nar_mcccode should be excluded from HMAC calculation`,
							'error'
						);
					}

					log(
						`<strong>✅ Source String Construction Test Passed</strong><br>
						<strong>BSP Field Order:</strong><br><pre>${expectedOrder}</pre><br>
						<strong>Generated Source String:</strong><br><pre>${result.sourceString}</pre><br>
						<strong>🔐 Generated Checksum:</strong><br><code>${result.checksum}</code>`,
						'success'
					);

					// Verify HMAC format (lowercase hex, 64 characters)
					const isValidFormat = /^[0-9a-f]{64}$/.test(result.checksum);
					if (isValidFormat) {
						log(
							`<strong>✅ HMAC Format Valid</strong><br>Lowercase hex, 64 characters: <code>${result.checksum}</code>`,
							'success'
						);
					} else {
						log(
							`<strong>❌ HMAC Format Invalid</strong><br>Expected: lowercase hex 64 chars, Got: ${result.checksum}`,
							'error'
						);
					}
				} catch (error) {
					log(`<strong>❌ Source String Test Failed</strong><br>Error: ${error.message}`, 'error');
				}

				// Test 3: Configuration validation (secure server-side config)
				try {
					await configService.ensureLoaded();
					configService.validateConfig();
					log(
						'<strong>✅ Secure Configuration Validation Passed</strong><br><em>Configuration loaded from secure server</em>',
						'success'
					);
				} catch (error) {
					log(`<strong>❌ Configuration Validation Failed</strong><br>Error: ${error.message}`, 'error');
				}

				// Test 4: Amount validation
				try {
					configService.validateAmount('1.00');
					configService.validateAmount('100.50');
					configService.validateAmount('0.01');
					log(
						'<strong>✅ Amount Validation Tests Passed</strong><br>Tested: $1.00, $100.50, $0.01',
						'success'
					);
				} catch (error) {
					log(`<strong>❌ Amount Validation Failed</strong><br>Error: ${error.message}`, 'error');
				}

				// Test 5: Invalid amount validation
				let invalidAmountTests = 0;
				let invalidAmountPassed = 0;

				const invalidAmounts = [
					{ value: '0.00', reason: 'zero amount' },
					{ value: '-1.00', reason: 'negative amount' },
					{ value: '1.234', reason: 'too many decimal places' },
					{ value: 'abc', reason: 'non-numeric' },
					{ value: '', reason: 'empty string' },
					{ value: '99999999999999999', reason: 'exceeds BSP IPG precision' },
				];

				for (const test of invalidAmounts) {
					invalidAmountTests++;
					try {
						configService.validateAmount(test.value);
						log(
							`<strong>❌ Invalid Amount Should Have Failed</strong><br>${test.value} (${test.reason}) was not rejected`,
							'error'
						);
					} catch (error) {
						invalidAmountPassed++;
						log(
							`<strong>✅ Invalid Amount Correctly Rejected</strong><br>${test.value} (${test.reason}): ${error.message}`,
							'success'
						);
					}
				}

				if (invalidAmountPassed === invalidAmountTests) {
					log(
						`<strong>✅ All Invalid Amount Tests Passed</strong><br>${invalidAmountPassed}/${invalidAmountTests} invalid amounts correctly rejected`,
						'success'
					);
				} else {
					log(
						`<strong>❌ Some Invalid Amount Tests Failed</strong><br>Only ${invalidAmountPassed}/${invalidAmountTests} invalid amounts were rejected`,
						'error'
					);
				}

				log('<h4>🎉 All Tests Completed Successfully!</h4>', 'info');
			}

			async function testCurrentConfig() {
				clearResults();
				log('<h4>⚙️ Testing Current Secure Production Configuration</h4>', 'info');

				try {
					// Load secure configuration
					await configService.ensureLoaded();

					// Display current config (sanitized - no sensitive data)
					const configSummary = configService.getConfigSummary();
					log(
						`<strong>📋 Secure Configuration Summary</strong><br>
						<pre>${JSON.stringify(configSummary, null, 2)}</pre>`,
						'info'
					);

					// Test with actual production data via secure API
					const now = new Date();
					const transactionTime = formatTransactionTime(now);
					const orderNumber = `SPCA-${transactionTime}-001`; // Updated format with hyphens

					const testPaymentData = {
						nar_cardType: 'EX',
						nar_merBankCode: '01',
						nar_merId: 'PRODUCTION_MERCHANT_ID',
						nar_merTxnTime: transactionTime,
						nar_msgType: 'AR',
						nar_orderNo: orderNumber,
						nar_paymentDesc: BSP_CONFIG.paymentDescription || 'SPCA Fiji Donation',
						nar_remitterEmail: '',
						nar_remitterMobile: '',
						nar_txnAmount: BSP_CONFIG.defaultAmount || '1.00',
						nar_txnCurrency: '242',
						nar_version: '1.0',
						nar_returnUrl: BSP_CONFIG.returnUrl,
						nar_mcccode: 'PRODUCTION_MCC_CODE',
						nar_Secure: 'MERSECURE',
					};

					log('<strong>🔒 Testing Production Configuration via Secure Server API</strong>', 'info');
					const result = await hmacService.testHMAC(null, testPaymentData);

					log(
						`<strong>✅ Production Data Test Passed</strong><br>
						<strong>💳 Test Payment Data:</strong><br>
						<pre>${JSON.stringify(testPaymentData, null, 2)}</pre>`,
						'success'
					);
					log(`<strong>🔗 Source String Generated:</strong><br><pre>${result.sourceString}</pre>`, 'info');
					log(`<strong>🔐 Generated Checksum:</strong><br><code>${result.checksum}</code>`, 'success');

					// Validate production credentials are being used (server replaces placeholders with actual values)
					const hasProductionMerchantId =
						result.sourceString.includes('PRODUCTION_MERCHANT_ID') || result.sourceString.length > 100; // Production data will be longer

					if (hasProductionMerchantId || !result.sourceString.includes('PRODUCTION_MERCHANT_ID')) {
						log(
							`<strong>✅ Production Merchant ID Processing</strong><br>Server-side credential replacement working`,
							'success'
						);
					} else {
						log(
							`<strong>⚠️ Production Merchant ID Placeholder</strong><br>Server should replace PRODUCTION_MERCHANT_ID with actual value`,
							'warning'
						);
					}

					// Verify nar_mcccode is excluded from HMAC but present in payload
					const hasMccCodeExcluded =
						!result.sourceString.includes('PRODUCTION_MCC_CODE') ||
						result.sourceString.split('|').length === 13; // BSP spec: 13 fields

					if (hasMccCodeExcluded) {
						log(
							`<strong>✅ MCC Code Correctly Excluded from HMAC</strong><br>MCC code excluded from source string as per BSP spec`,
							'success'
						);
					} else {
						log(
							`<strong>❌ MCC Code Incorrectly Included in HMAC</strong><br>MCC code should be excluded from HMAC calculation`,
							'error'
						);
					}

					log(
						`<strong>🚀 Ready for BSP IPG Submission!</strong><br>
						✅ Production credentials configured via environment variables<br>
						✅ MCC Code excluded from HMAC (included in POST only)<br>
						✅ HMAC SHA-256 calculation working correctly<br>
						<em>🔒 All sensitive operations performed securely on server</em>`,
						'success'
					);
				} catch (error) {
					log(`<strong>❌ Production Configuration Test Failed</strong><br>Error: ${error.message}`, 'error');
				}
			}

			async function testEnvironmentVars() {
				clearResults();
				log('<h4>🔧 Testing Environment Variables Configuration</h4>', 'info');

				try {
					log('<strong>🔍 Checking BSP IPG Environment Variables...</strong>', 'info');

					// Call the environment check API
					const response = await fetch('/api/env/check', {
						method: 'GET',
						headers: {
							'Content-Type': 'application/json',
						},
					});

					const data = await response.json();

					if (!data.success) {
						throw new Error(data.error || 'Environment check failed');
					}

					// Display overall status
					const statusType = data.overallStatus === 'PASS' ? 'success' : 'error';
					const statusIcon = data.overallStatus === 'PASS' ? '✅' : '❌';

					log(
						`<strong>${statusIcon} Overall Status: ${data.overallStatus}</strong><br>
						Environment: <code>${data.environment}</code><br>
						Completion: <strong>${data.completionPercentage}%</strong> (${data.summary.required.present}/${data.summary.required.total} required variables)`,
						statusType
					);

					// Display summary
					log(
						`<strong>📊 Configuration Summary</strong><br>
						<strong>Required Variables:</strong> ${data.summary.required.present}/${data.summary.required.total} configured<br>
						<strong>Optional Variables:</strong> ${data.summary.optional.present}/${data.summary.optional.total} configured<br>
						<strong>Missing Required:</strong> ${data.summary.required.missing}<br>
						<em>Checked at: ${new Date(data.timestamp).toLocaleString()}</em>`,
						'info'
					);

					// Display detailed variable status
					let requiredVarsHtml = '<strong>🔑 Required Variables Status:</strong><br><br>';
					let optionalVarsHtml = '<strong>⚙️ Optional Variables Status:</strong><br><br>';
					let bspSpecificHtml = '<strong>🏦 BSP IPG Specific Variables:</strong><br><br>';

					// BSP-specific critical variables
					const bspCriticalVars = ['BSP_HMAC_KEY', 'MERCHANT_ID', 'MCC_CODE', 'BSP_PAYMENT_URL'];

					Object.entries(data.variables).forEach(([varName, varInfo]) => {
						const statusIcon = varInfo.status === 'OK' ? '✅' : varInfo.status === 'EMPTY' ? '⚠️' : '❌';
						const statusClass =
							varInfo.status === 'OK'
								? 'status-ok'
								: varInfo.status === 'EMPTY'
								? 'status-empty'
								: 'status-missing';

						const varHtml = `<span class="env-status ${statusClass}">${statusIcon} <code>${varName}</code>: ${varInfo.status}</span><br>`;

						if (bspCriticalVars.includes(varName)) {
							bspSpecificHtml += varHtml;
						} else if (varInfo.required) {
							requiredVarsHtml += varHtml;
						} else {
							optionalVarsHtml += varHtml;
						}
					});

					log(bspSpecificHtml, data.overallStatus === 'PASS' ? 'success' : 'error');
					log(requiredVarsHtml, data.overallStatus === 'PASS' ? 'success' : 'error');
					log(optionalVarsHtml, 'info');

					// Specific BSP production credential validation
					const merchantIdVar = data.variables['MERCHANT_ID'];
					const mccCodeVar = data.variables['MCC_CODE'];

					if (merchantIdVar && merchantIdVar.status === 'OK') {
						log(
							`<strong>✅ BSP Production Merchant ID Configured</strong><br>MERCHANT_ID environment variable is set`,
							'success'
						);
					} else {
						log(
							`<strong>❌ BSP Production Merchant ID Missing</strong><br>Required: MERCHANT_ID environment variable`,
							'error'
						);
					}

					if (mccCodeVar && mccCodeVar.status === 'OK') {
						log(
							`<strong>✅ BSP Production MCC Code Configured</strong><br>MCC_CODE environment variable is set`,
							'success'
						);
					} else {
						log(
							`<strong>❌ BSP Production MCC Code Missing</strong><br>Required: MCC_CODE environment variable`,
							'error'
						);
					}

					// Display recommendations if there are issues
					if (data.overallStatus !== 'PASS') {
						let recommendations = '<strong>🔧 Recommendations:</strong><br><br>';

						Object.entries(data.variables).forEach(([varName, varInfo]) => {
							if (varInfo.required && varInfo.status !== 'OK') {
								if (varInfo.status === 'MISSING') {
									recommendations += `• Add <code>${varName}</code> to your environment variables<br>`;
								} else if (varInfo.status === 'EMPTY') {
									recommendations += `• Set a value for <code>${varName}</code> (currently empty)<br>`;
								}
							}
						});

						recommendations += '<br><strong>For Vercel deployment:</strong><br>';
						recommendations += '• Go to Project Settings → Environment Variables<br>';
						recommendations += '• Add missing variables for Production, Preview, and Development<br>';
						recommendations += '<br><strong>For local development:</strong><br>';
						recommendations += '• Check your <code>server/.env</code> file<br>';
						recommendations += '• Copy from <code>server/.env.example</code> if needed<br>';

						log(recommendations, 'error');
					} else {
						log(
							'<strong>🎉 Environment Configuration Complete!</strong><br>' +
								'All required BSP IPG environment variables are properly configured.<br>' +
								'<em>Your application is ready for secure payment processing.</em>',
							'success'
						);
					}
				} catch (error) {
					log(`<strong>❌ Environment Variables Test Failed</strong><br>Error: ${error.message}`, 'error');
					log(
						'<strong>💡 Troubleshooting:</strong><br>' +
							'• Ensure the server is running<br>' +
							'• Check that <code>/api/env/check</code> endpoint is accessible<br>' +
							'• Verify environment variables are set correctly',
						'info'
					);
				}
			}

			function formatTransactionTime(date) {
				const year = date.getFullYear();
				const month = String(date.getMonth() + 1).padStart(2, '0');
				const day = String(date.getDate()).padStart(2, '0');
				const hours = String(date.getHours()).padStart(2, '0');
				const minutes = String(date.getMinutes()).padStart(2, '0');
				const seconds = String(date.getSeconds()).padStart(2, '0');

				return `${year}${month}${day}${hours}${minutes}${seconds}`;
			}

			// Auto-run tests on page load
			window.addEventListener('load', () => {
				log('HMAC Test Page Loaded - Ready to run tests', 'info');
			});
		</script>
	</body>
</html>
