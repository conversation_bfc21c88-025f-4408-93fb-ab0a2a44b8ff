# BSP IPG Environment Configuration - SPCA Fiji
#
# FOR LOCAL DEVELOPMENT:
# 1. Copy this file to .env in the same directory (server/.env)
# 2. Fill in your actual values
# 3. Keep .env file secure and never commit to version control
#
# FOR VERCEL DEPLOYMENT:
# 1. Go to Vercel Project Settings > Environment Variables
# 2. Add each variable below with your actual values
# 3. Set Environment: Production, Preview, Development as needed

# ===== PRODUCTION BSP IPG CREDENTIALS =====
# These are the core credentials provided by BSP for payment processing
BSP_HMAC_KEY=your_production_hmac_key_here

# BSP Merchant ID Components (as per BSP documentation: MID+SID)
MERCHANT_MID=your_merchant_mid_here
MERCHANT_SID=your_merchant_sid_here
MERCHANT_ID=your_merchant_id_here

# BSP Merchant Configuration
MCC_CODE=your_mcc_code_here
MERCHANT_NAME=Society for the Prevention of Cruelty to Animals (SPCA) Fiji Islands

# ===== PRODUCTION URLS =====
# BSP IPG gateway and return URLs
BSP_PAYMENT_URL=https://oceania.thecardservicesonline.com/bsppg/mercpg
BSP_PORTAL_URL=https://oceania.thecardservicesonline.com/merchantportal/bsp/html
RETURN_URL=https://your-domain.com/thank-you
WEBSITE_ADDRESS=https://your-domain.com

# ===== SERVER CONFIGURATION =====
# Node.js server settings
PORT=3000
NODE_ENV=production
CORS_ORIGIN=https://your-domain.com

# ===== SECURITY CONFIGURATION =====
# Rate limiting and security settings
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# ===== ENVIRONMENT SWITCHING =====
# Controls which environment to use (uat|prod)
BSP_ENV=prod

# ===== BUSINESS CONFIGURATION =====
# Payment form and validation settings
BSP_PAYMENT_DESCRIPTION=SPCA Fiji Donation
DEFAULT_AMOUNT=1.00
MIN_AMOUNT=0.01
MAX_AMOUNT=999999.99
AMOUNT_DECIMAL_PLACES=2
ENABLE_HMAC_TESTING=true

# ===== UAT ENVIRONMENT (OPTIONAL) =====
# UAT testing environment variables
BSP_HMAC_KEY_UAT=your_uat_hmac_key_here
BSP_PAYMENT_URL_UAT=https://uat2.yalamanchili.in/MPI_v1/mercpg
BSP_PORTAL_URL_UAT=https://uat2.yalamanchili.in/merchantportal/bsp/html
MERCHANT_MID_UAT=your_uat_merchant_mid_here
MERCHANT_SID_UAT=your_uat_merchant_sid_here
MERCHANT_ID_UAT=your_uat_merchant_id_here
MCC_CODE_UAT=your_uat_mcc_code_here
