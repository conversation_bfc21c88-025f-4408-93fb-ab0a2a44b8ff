/**
 * BSP IPG Configuration Service
 * Loads configuration from server API
 */

let BSP_CONFIG = {
    // Application Configuration
    DEBUG_MODE: true,
    MAX_DEBUG_MESSAGES: 100,
    PAYMENT_TIMEOUT: 30000, // 30 seconds

    // API Configuration
    API_BASE_URL: window.location.origin + '/api',

    // Will be populated from server configuration
    merchantName: null,
    returnUrl: null,
    websiteAddress: null,
    environment: null,
    currency: null,
    currencyName: null,
    paymentDescription: null,
    defaultAmount: null,
    minAmount: null,
    maxAmount: null,
    decimalPlaces: null
};

// Configuration Service
class ConfigService {
    constructor() {
        this.isLoaded = false;
        this.loadPromise = null;
    }

    // Load configuration from server API
    async loadConfig() {
        if (this.loadPromise) {
            return this.loadPromise;
        }

        this.loadPromise = this._fetchConfig();
        return this.loadPromise;
    }

    async _fetchConfig() {
        try {
            const response = await fetch(`${BSP_CONFIG.API_BASE_URL}/config`);

            if (!response.ok) {
                throw new Error(`Config fetch failed: ${response.status}`);
            }

            const data = await response.json();

            if (!data.success) {
                throw new Error(data.error || 'Config fetch failed');
            }

            // Update BSP_CONFIG with server data
            Object.assign(BSP_CONFIG, data.config);
            this.isLoaded = true;

            console.log('✅ Configuration loaded from server');
            return data.config;

        } catch (error) {
            console.error('❌ Failed to load configuration:', error.message);
            throw new Error(`Configuration loading failed: ${error.message}`);
        }
    }

    // Validate configuration is loaded
    validateConfig() {
        if (!this.isLoaded) {
            throw new Error('Configuration not loaded. Call loadConfig() first.');
        }

        const required = ['merchantName', 'currency', 'returnUrl'];
        const missing = required.filter(field => !BSP_CONFIG[field]);

        if (missing.length > 0) {
            throw new Error(`Missing required configuration: ${missing.join(', ')}`);
        }

        // Validate currency code (now dynamic from server)
        if (!BSP_CONFIG.currency) {
            throw new Error('Currency code not configured');
        }

        // Validate URLs
        if (!this.isValidUrl(BSP_CONFIG.returnUrl)) {
            throw new Error('Invalid return URL');
        }

        return true;
    }

    /**
     * Ensure configuration is loaded
     */
    async ensureLoaded() {
        if (!this.isLoaded) {
            await this.loadConfig();
        }
    }

    /**
     * Check if URL is valid
     */
    isValidUrl(url) {
        try {
            new URL(url);
            return url.startsWith('https://');
        } catch {
            return false;
        }
    }

    /**
     * Get configuration summary for debugging (no sensitive data)
     */
    getConfigSummary() {
        return {
            merchantName: BSP_CONFIG.merchantName,
            returnUrl: BSP_CONFIG.returnUrl,
            currency: BSP_CONFIG.currency,
            environment: BSP_CONFIG.environment || 'Production',
            defaultAmount: BSP_CONFIG.DEFAULT_AMOUNT,
            loaded: this.isLoaded
        };
    }

    /**
     * Validate payment amount
     */
    validateAmount(amount) {
        const numAmount = parseFloat(amount);

        if (isNaN(numAmount)) {
            throw new Error('Amount must be a valid number');
        }

        // Use fallback values if config not loaded
        const minAmount = BSP_CONFIG.minAmount || 0.01;
        const maxAmount = BSP_CONFIG.maxAmount || 999999.99;
        const decimalPlaces = BSP_CONFIG.decimalPlaces || 2;

        // BSP IPG requires amount > 0
        if (numAmount <= 0) {
            throw new Error('Amount must be greater than zero');
        }

        if (numAmount < minAmount) {
            throw new Error(`Amount must be at least FJD ${minAmount}`);
        }

        if (numAmount > maxAmount) {
            throw new Error(`Amount cannot exceed FJD ${maxAmount.toLocaleString()}`);
        }

        // Check decimal places (BSP IPG nar_txnAmount format: 16,2)
        const decimalStr = numAmount.toString().split('.')[1] || '';
        if (decimalStr.length > decimalPlaces) {
            throw new Error(`Amount cannot have more than ${decimalPlaces} decimal places`);
        }

        // BSP IPG total length validation (16 digits including decimals)
        const totalDigits = numAmount.toString().replace('.', '').length;
        if (totalDigits > 16) {
            throw new Error('Amount exceeds BSP IPG maximum precision (16 digits)');
        }

        return true;
    }

    /**
     * Format amount for BSP IPG
     */
    formatAmount(amount) {
        return parseFloat(amount).toFixed(BSP_CONFIG.decimalPlaces || 2);
    }

    /**
     * Get environment info (no sensitive data)
     */
    getEnvironmentInfo() {
        return {
            environment: BSP_CONFIG.environment || 'Production',
            merchant: BSP_CONFIG.merchantName,
            currency: `${BSP_CONFIG.currencyName || 'FJD'} (${BSP_CONFIG.currency || '242'})`,
            securityType: `${BSP_CONFIG.securityType || 'MERSECURE'} (Server-side HMAC)`,
            apiEndpoint: BSP_CONFIG.API_BASE_URL
        };
    }
}

// Create global instances
window.BSP_CONFIG = BSP_CONFIG;
window.configService = new ConfigService();

// Convenience function for backward compatibility
window.validateConfig = () => window.configService.validateConfig();
