/**
 * Secure HMAC Service for BSP IPG Integration
 * HMAC calculation is now handled server-side for security
 * Client-side service provides testing and verification utilities only
 */

class HMACService {
    constructor() {
        this.debugService = null;
    }

    /**
     * SECURITY NOTE: HMAC calculation moved to server-side
     * This method is deprecated for production use
     */
    async calculateChecksum(paymentData) {
        throw new Error('HMAC calculation moved to secure server-side API for security. Use server /api/payment/initiate endpoint.');
    }

    /**
     * Test HMAC calculation (development only)
     * Uses server-side API for secure testing
     */
    async testHMAC(message, testData = null) {
        try {
            const response = await fetch(`${BSP_CONFIG.API_BASE_URL}/hmac/test`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ message, testData })
            });

            if (!response.ok) {
                throw new Error(`HMAC test failed: ${response.status}`);
            }

            const result = await response.json();

            if (!result.success) {
                throw new Error(result.error || 'HMAC test failed');
            }

            return result;

        } catch (error) {
            this.log('error', `HMAC test failed: ${error.message}`);
            throw error;
        }
    }

    /**
     * NOTE: Source string construction moved to server-side for security
     * This method is kept for reference only
     */
    constructSourceString(data) {
        throw new Error('Source string construction moved to secure server-side API for security.');
    }

    /**
     * SECURITY NOTE: Direct HMAC calculation removed from client-side
     * All HMAC operations now handled server-side for security
     */
    async hmacSHA256(message, key) {
        throw new Error('Direct HMAC calculation not allowed on client-side for security. Use server API.');
    }

    /**
     * NOTE: Cryptographic utility functions removed from client-side
     * All cryptographic operations now handled server-side for security
     */

    /**
     * Verify response checksum using secure server API
     * @param {Object} responseData - Response data from BSP IPG
     * @param {string} receivedChecksum - Checksum received from BSP
     * @returns {Promise<boolean>} - Verification result
     */
    async verifyResponseChecksum(responseData, receivedChecksum) {
        try {
            const response = await fetch(`${BSP_CONFIG.API_BASE_URL}/payment/verify`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(responseData)
            });

            if (!response.ok) {
                throw new Error(`Verification request failed: ${response.status}`);
            }

            const result = await response.json();

            if (!result.success) {
                this.log('error', `Response verification failed: ${result.error}`);
                return false;
            }

            const isValid = result.checksumValid;

            this.log(isValid ? 'success' : 'error',
                `Response checksum verification: ${isValid ? 'VALID' : 'INVALID'}`);

            return isValid;

        } catch (error) {
            this.log('error', `Response checksum verification failed: ${error.message}`);
            return false;
        }
    }

    /**
     * NOTE: Response source string construction moved to server-side for security
     * This method is kept for reference only
     */
    constructResponseSourceString(data) {
        throw new Error('Response source string construction moved to secure server-side API for security.');
    }

    /**
     * Validate checksum format
     */
    isValidChecksumFormat(checksum) {
        // HMAC SHA-256 produces 64 character hex string
        return typeof checksum === 'string' && 
               checksum.length === 64 && 
               /^[0-9a-fA-F]+$/.test(checksum);
    }

    /**
     * Log debug messages
     */
    log(level, message) {
        if (this.debugService) {
            this.debugService.log(level, message);
        }
    }

    /**
     * Set debug service reference
     */
    setDebugService(debugService) {
        this.debugService = debugService;
    }

    /**
     * Get service status
     */
    getStatus() {
        return {
            initialized: true,
            cryptoSupported: !!window.crypto?.subtle,
            debugEnabled: !!this.debugService
        };
    }
}

// Create global instance
window.hmacService = new HMACService();
