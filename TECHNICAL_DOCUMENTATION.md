# Technical Documentation - SPCA Fiji BSP IPG Integration

## 🏗️ System Architecture

### **Overview**
The SPCA Fiji donation platform integrates with Bank of South Pacific's Internet Payment Gateway (BSP IPG) using HMAC SHA-256 authentication for secure payment processing.

### **Technology Stack**
- **Frontend**: HTML5, CSS3, Vanilla JavaScript
- **Backend**: Node.js with Express.js
- **Deployment**: Vercel Serverless Functions
- **Security**: HMAC SHA-256 cryptographic signing
- **Database**: None (stateless payment processing)

### **Architecture Diagram**
```
[Donor Browser] 
    ↓ (HTTPS)
[Vercel Frontend] 
    ↓ (API Call)
[Vercel Serverless Function] 
    ↓ (HMAC Generation)
[BSP IPG Gateway]
    ↓ (Payment Processing)
[BSP Banking System]
    ↓ (Response)
[Thank You Page]
```

## 🔐 Security Implementation

### **HMAC SHA-256 Authentication**

#### **Source String Construction**
BSP IPG requires a 13-field source string in specific order:
```
nar_cardType|nar_merBankCode|nar_merId|nar_merTxnTime|nar_msgType|nar_orderNo|nar_paymentDesc|nar_remitterEmail|nar_remitterMobile|nar_txnAmount|nar_txnCurrency|nar_version|nar_returnUrl
```

#### **HMAC Generation Process**
1. **Construct Source String**: Concatenate 13 fields with pipe separators
2. **UTF-8 Encoding**: Convert source string to UTF-8 bytes
3. **HMAC SHA-256**: Sign with BSP-provided HMAC key
4. **Hex Conversion**: Convert to lowercase hexadecimal string

#### **Implementation**
<augment_code_snippet path="server/routes/api.js" mode="EXCERPT">
````javascript
// Generate HMAC SHA-256 checksum
const crypto = require('crypto');
const sourceString = constructSourceString(formData);
const hmac = crypto.createHmac('sha256', bspConfig.HMAC_KEY);
hmac.update(sourceString, 'utf8');
const checksum = hmac.digest('hex').toLowerCase();
````
</augment_code_snippet>

### **Environment Security**
- **Server-side Only**: HMAC key never exposed to client
- **Environment Variables**: Secure credential storage
- **HTTPS Only**: All communications encrypted
- **Rate Limiting**: Protection against abuse

## 📡 API Endpoints

### **POST /api/payment/initiate**
Initiates payment with BSP IPG.

**Request Body:**
```json
{
  "amount": "1.00",
  "email": "<EMAIL>",
  "mobile": "**********"
}
```

**Response:**
```json
{
  "success": true,
  "redirectUrl": "https://oceania.thecardservicesonline.com/bsppg/mercpg",
  "formData": {
    "nar_merId": "***************",
    "nar_txnAmount": "1.00",
    "nar_checkSum": "74f10ef139a4ddddd1a1fc2b91c4160ab82476ecdeae579b820258f9c5231b1e"
  }
}
```

### **GET /api/env/check**
Environment configuration validation.

**Response:**
```json
{
  "status": "healthy",
  "environment": "production",
  "requiredVars": {
    "BSP_HMAC_KEY": "✅ Set",
    "MERCHANT_ID": "✅ Set (15 chars)",
    "MCC_CODE": "✅ Set"
  }
}
```

### **POST /api/hmac/test**
HMAC testing and validation endpoint.

**Request Body:**
```json
{
  "testData": {
    "nar_merId": "***************",
    "nar_txnAmount": "1.00"
  }
}
```

## 🔧 Configuration Management

### **Environment Variables**

#### **Required Production Variables**
```bash
# BSP IPG Credentials
BSP_HMAC_KEY=fe6d12d27af07e95b503d5b901017e945436783cb6d8694e12384d125c5837a1c24fc7d862c590c4a36e2a8fbbef5957c11580a69c43b9120aca57568901f2a0
MERCHANT_ID=***************
MCC_CODE=8398
MERCHANT_NAME=Society for the Prevention of Cruelty to Animals (SPCA) Fiji Islands

# URLs
BSP_PAYMENT_URL=https://oceania.thecardservicesonline.com/bsppg/mercpg
RETURN_URL=https://donations.spcafiji.com/thank-you
WEBSITE_ADDRESS=https://donations.spcafiji.com

# Server Configuration
NODE_ENV=production
CORS_ORIGIN=https://donations.spcafiji.com
```

#### **Optional Variables**
```bash
# Security
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Business Configuration
BSP_PAYMENT_DESCRIPTION=SPCA Fiji Donation
DEFAULT_AMOUNT=1.00
MIN_AMOUNT=0.01
MAX_AMOUNT=999999.99
```

### **Configuration Loading**
<augment_code_snippet path="server/config/config.js" mode="EXCERPT">
````javascript
class Config {
    getBSPConfig() {
        const bspEnv = process.env.BSP_ENV || 'prod';
        
        const config = {
            HMAC_KEY: process.env.BSP_HMAC_KEY,
            MERCHANT_ID: process.env.MERCHANT_ID,
            MCC_CODE: process.env.MCC_CODE,
            BSP_PAYMENT_URL: process.env.BSP_PAYMENT_URL
        };
        
        return config;
    }
}
````
</augment_code_snippet>

## 🧪 Testing Framework

### **HMAC Testing**
The system includes comprehensive HMAC testing capabilities:

**Test Page**: `/test-hmac.html`
- **Source String Verification**: Shows exact field construction
- **Checksum Generation**: Displays generated HMAC SHA-256
- **BSP Compliance**: Validates against BSP IPG specification

### **Environment Testing**
**Endpoint**: `/api/env/check`
- **Variable Validation**: Checks all required environment variables
- **Format Verification**: Validates HMAC key and merchant ID formats
- **Health Status**: Overall system health check

### **Integration Testing**
- **End-to-End**: Complete payment flow testing
- **Error Scenarios**: Invalid input handling
- **Security Testing**: HMAC validation and rate limiting

## 📊 Monitoring & Logging

### **Request Logging**
All payment requests are logged with:
- **Timestamp**: Request time
- **Source String**: Generated for HMAC
- **Checksum**: Generated HMAC SHA-256
- **Response**: BSP IPG response status

### **Error Tracking**
- **Validation Errors**: Input validation failures
- **HMAC Errors**: Checksum generation issues
- **BSP Errors**: Gateway response errors
- **System Errors**: Server-side exceptions

### **Performance Monitoring**
- **Response Times**: API endpoint performance
- **Success Rates**: Payment completion rates
- **Error Rates**: Failure analysis and trends

## 🔄 Deployment Process

### **Vercel Deployment**
1. **Environment Variables**: Configure in Vercel dashboard
2. **Build Process**: Automatic deployment from Git
3. **Domain Configuration**: Custom domain setup
4. **SSL Certificate**: Automatic HTTPS provisioning

### **Environment Switching**
The system supports multiple environments:
- **Production**: Live BSP IPG integration
- **UAT**: BSP testing environment
- **Development**: Local development setup

### **Configuration Management**
<augment_code_snippet path="server/.env.example" mode="EXCERPT">
````bash
# FOR LOCAL DEVELOPMENT:
# 1. Copy this file to .env in the same directory (server/.env)
# 2. Fill in your actual values

# FOR VERCEL DEPLOYMENT:
# 1. Go to Vercel Project Settings > Environment Variables
# 2. Add each variable below with your actual values
````
</augment_code_snippet>

## 🛠️ Development Setup

### **Local Development**
1. **Clone Repository**: `git clone https://github.com/vikichand/donations.spcafiji.com.git`
2. **Install Dependencies**: `npm install`
3. **Environment Setup**: Copy `server/.env.example` to `server/.env`
4. **Start Server**: `npm run dev`

### **Required Tools**
- **Node.js**: v18+ recommended
- **npm**: Package management
- **Git**: Version control
- **Code Editor**: VS Code recommended

### **Development Workflow**
1. **Feature Development**: Create feature branch
2. **Testing**: Run local tests and HMAC validation
3. **Code Review**: Pull request review process
4. **Deployment**: Automatic deployment via Vercel

## 📚 BSP IPG Integration Details

### **Field Mapping**
| BSP Field | Variable | Description | Example |
|-----------|----------|-------------|---------|
| `nar_cardType` | Fixed | Card type | `EX` |
| `nar_merBankCode` | Fixed | Bank code | `01` |
| `nar_merId` | `MERCHANT_ID` | Merchant ID | `***************` |
| `nar_merTxnTime` | Generated | Transaction time | `**************` |
| `nar_msgType` | Fixed | Message type | `AR` |
| `nar_orderNo` | Generated | Order number | `SPCA-**************-001` |
| `nar_paymentDesc` | `BSP_PAYMENT_DESCRIPTION` | Description | `SPCA Fiji Donation` |
| `nar_remitterEmail` | User Input | Email | `<EMAIL>` |
| `nar_remitterMobile` | User Input | Mobile | `**********` |
| `nar_txnAmount` | User Input | Amount | `1.00` |
| `nar_txnCurrency` | Fixed | Currency | `242` (FJD) |
| `nar_version` | Fixed | Version | `1.0` |
| `nar_returnUrl` | `RETURN_URL` | Return URL | `https://donations.spcafiji.com/thank-you` |

### **BSP IPG Compliance**
- **Manual Version**: BSP IPG Manual v2.54
- **Authentication**: HMAC SHA-256
- **Field Count**: 13 fields in source string
- **Character Encoding**: UTF-8
- **Hash Format**: Lowercase hexadecimal

## 🔍 Troubleshooting

### **Common Issues**

#### **Invalid Checksum Error**
- **Cause**: HMAC key mismatch or source string construction error
- **Solution**: Verify HMAC key and field order using `/test-hmac.html`

#### **Environment Variable Missing**
- **Cause**: Required environment variable not set
- **Solution**: Check `/api/env/check` and update configuration

#### **Payment Gateway Timeout**
- **Cause**: Network connectivity or BSP IPG service issues
- **Solution**: Check BSP IPG status and retry

### **Debug Tools**
- **HMAC Test Page**: `/test-hmac.html`
- **Environment Check**: `/api/env/check`
- **Server Logs**: Vercel function logs
- **Browser Console**: Client-side error messages

## 📞 Support & Maintenance

### **BSP IPG Support**
- **Technical Support**: BSP IPG technical team
- **Documentation**: BSP IPG Merchant Integration Manual v2.54
- **Portal**: BSP Merchant Portal for transaction monitoring

### **System Maintenance**
- **Regular Updates**: Keep dependencies updated
- **Security Patches**: Apply security updates promptly
- **Performance Monitoring**: Monitor response times and error rates
- **Backup Strategy**: Environment configuration backup

### **Contact Information**
- **Developer**: Vikash Chand (<EMAIL>)
- **Repository**: https://github.com/vikichand/donations.spcafiji.com
- **Deployment**: Vercel (donations.spcafiji.com)
