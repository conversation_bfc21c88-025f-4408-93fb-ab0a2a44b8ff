/**
 * BSP IPG Payment Service
 * Handles payment processing and AR message generation
 */

const hmacService = require('./hmac-service');
const config = require('../config/config');

class PaymentService {
    constructor() {
        this.bspConfig = config.getBSPConfig();
        this.publicConfig = config.getPublicConfig();
    }

    // Process payment initiation request
    async initiatePayment(amount) {
        try {
            console.log(`[Payment] Initiating payment for FJD ${amount}`);
            
            // Validate amount
            this.validateAmount(amount);

            // Generate payment data
            const paymentData = this.generatePaymentData(amount);

            // Calculate checksum
            const checksum = hmacService.calculateChecksum(paymentData);
            paymentData.nar_checkSum = checksum;

            console.log(`[Payment] Payment data generated successfully`);
            console.log(`[Payment] Order: ${paymentData.nar_orderNo}`);
            console.log(`[Payment] Amount: FJD ${paymentData.nar_txnAmount}`);
            console.log(`[Payment] Checksum: ${checksum.length} chars`);

            // Return form data with checksum
            return {
                success: true,
                formData: paymentData,
                bspPaymentUrl: this.bspConfig.BSP_PAYMENT_URL,
                orderNumber: paymentData.nar_orderNo,
                amount: paymentData.nar_txnAmount,
                currency: 'FJD'
            };
            
        } catch (error) {
            console.error(`[Payment] Payment initiation failed:`, error.message);
            throw new Error(`Payment initiation failed: ${error.message}`);
        }
    }

    // Generate BSP IPG AR message data
    generatePaymentData(amount) {
        const now = new Date();
        const transactionTime = this.formatTransactionTime(now);
        const orderNumber = this.generateOrderNumber(now);

        const paymentData = {
            // BSP IPG AR message fields
            nar_msgType: 'AR',
            nar_merTxnTime: transactionTime,
            nar_orderNo: orderNumber,
            nar_merId: this.bspConfig.MERCHANT_ID,
            nar_merBankCode: '01',
            nar_txnCurrency: '242',
            nar_txnAmount: this.formatAmount(amount),
            nar_paymentDesc: this.publicConfig.paymentDescription,
            nar_version: '1.0',
            nar_returnUrl: this.bspConfig.RETURN_URL,
            nar_mcccode: this.bspConfig.MCC_CODE,
            nar_Secure: 'MERSECURE',
            nar_remitterEmail: '',
            nar_remitterMobile: '',
            nar_cardType: 'EX'
        };

        return paymentData;
    }

    // Format transaction time as YYYYMMDDHHMMSS
    formatTransactionTime(date) {
        // Time sanity check - ensure transaction time is not too far from current time
        const now = Date.now();
        const txnTime = date.getTime();
        const timeDiff = Math.abs(now - txnTime);
        const maxDiff = 10 * 60 * 1000; // 10 minutes

        if (timeDiff > maxDiff) {
            throw new Error(`Transaction time too far from current time: ${timeDiff}ms difference`);
        }

        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        const seconds = String(date.getSeconds()).padStart(2, '0');

        const formatted = `${year}${month}${day}${hours}${minutes}${seconds}`;
        console.log(`[Payment] Transaction time formatted: ${formatted} (diff: ${timeDiff}ms)`);

        return formatted;
    }

    /**
     * Generate unique order number with safe charset
     */
    generateOrderNumber(date) {
        const timestamp = this.formatTransactionTime(date);
        const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
        const orderNo = `SPCA-${timestamp}-${random}`;

        // Normalize to safe charset and cap length
        return this.normalizeOrderNo(orderNo);
    }

    /**
     * Normalize order number to safe charset
     */
    normalizeOrderNo(orderNo) {
        // Replace underscores and any odd chars with hyphens, cap length to 30
        const normalized = orderNo.replace(/[^A-Za-z0-9-]/g, '-').slice(0, 30);
        console.log(`[Payment] Order number normalized: ${orderNo} -> ${normalized}`);
        return normalized;
    }

    /**
     * Validate payment amount
     */
    validateAmount(amount) {
        const numAmount = parseFloat(amount);

        if (isNaN(numAmount)) {
            throw new Error('Amount must be a valid number');
        }

        // BSP IPG requires amount > 0
        if (numAmount <= 0) {
            throw new Error('Amount must be greater than zero');
        }

        // Use configured limits
        const minAmount = this.publicConfig.minAmount;
        const maxAmount = this.publicConfig.maxAmount;
        const decimalPlaces = this.publicConfig.decimalPlaces;

        if (numAmount < minAmount) {
            throw new Error(`Amount must be at least FJD ${minAmount}`);
        }

        if (numAmount > maxAmount) {
            throw new Error(`Amount cannot exceed FJD ${maxAmount.toLocaleString()}`);
        }

        // Check decimal places (BSP IPG nar_txnAmount format: 16,2)
        const decimalStr = numAmount.toString().split('.')[1] || '';
        if (decimalStr.length > decimalPlaces) {
            throw new Error(`Amount cannot have more than ${decimalPlaces} decimal places`);
        }

        // BSP IPG total length validation (16 digits including decimals)
        const totalDigits = numAmount.toString().replace('.', '').length;
        if (totalDigits > 16) {
            throw new Error('Amount exceeds BSP IPG maximum precision (16 digits)');
        }

        return true;
    }

    /**
     * Format amount for BSP IPG
     */
    formatAmount(amount) {
        return parseFloat(amount).toFixed(2);
    }

    /**
     * Handle payment response verification
     * @param {Object} responseData - Response data from BSP IPG
     * @returns {Object} - Verification result
     */
    verifyPaymentResponse(responseData) {
        try {
            console.log(`[Payment] Verifying payment response for order: ${responseData.nar_orderNo}`);

            let isChecksumValid = true;
            
            // Verify checksum if present
            if (responseData.nar_checkSum) {
                isChecksumValid = hmacService.verifyResponseChecksum(responseData, responseData.nar_checkSum);
            }

            // Determine payment status
            const status = this.getPaymentStatus(responseData);

            const result = {
                success: status === 'SUCCESS',
                status: status,
                checksumValid: isChecksumValid,
                orderNumber: responseData.nar_orderNo,
                transactionId: responseData.nar_narTxnId,
                amount: responseData.nar_txnAmount,
                authCode: responseData.nar_debitAuthCode,
                remarks: responseData.nar_remarks,
                cardType: responseData.nar_cardType,
                remitterName: responseData.nar_remitterName
            };

            console.log(`[Payment] Response verification completed: ${status}`);
            
            return result;
            
        } catch (error) {
            console.error(`[Payment] Response verification failed:`, error.message);
            return {
                success: false,
                status: 'ERROR',
                error: error.message
            };
        }
    }

    /**
     * Get payment status from response
     */
    getPaymentStatus(responseData) {
        const remarks = responseData.nar_remarks;
        const authCode = responseData.nar_debitAuthCode;
        
        if (remarks === 'Approved' && authCode === '00') {
            return 'SUCCESS';
        } else if (remarks) {
            return 'FAILED';
        } else {
            return 'UNKNOWN';
        }
    }
}

module.exports = new PaymentService();
