/**
 * Server-Side HMAC SHA-256 Service for BSP IPG Integration
 * Implements secure checksum calculation using Node.js crypto module
 * Handles both AR (Authorization Request) and AC (Authorization Confirmation) messages
 */

const crypto = require('crypto');
const config = require('../config/config');

class HMACService {
    constructor() {
        this.bspConfig = config.getBSPConfig();
    }

    /**
     * Calculate HMAC SHA-256 checksum for BSP IPG
     * @param {Object} paymentData - Payment data object
     * @returns {string} - Hex encoded checksum
     */
    calculateChecksum(paymentData) {
        try {
            // Step 1: Construct source string according to BSP manual
            const sourceString = this.constructSourceString(paymentData);
            
            console.log(`[HMAC] Source string constructed, length: ${sourceString.length}`);
            
            // Step 2: Calculate HMAC SHA-256
            const checksum = this.hmacSHA256(sourceString, this.bspConfig.HMAC_KEY);
            
            console.log(`[HMAC] Checksum calculated successfully, length: ${checksum.length}`);
            
            return checksum;
            
        } catch (error) {
            console.error(`[HMAC] Checksum calculation failed:`, error.message);
            throw new Error(`Checksum calculation failed: ${error.message}`);
        }
    }

    /**
     * Construct source string for AR message according to BSP manual
     * Format: nar_cardType|nar_merBankCode|nar_merId|nar_merTxnTime|nar_msgType|nar_orderNo|nar_paymentDesc|nar_remitterEmail|nar_remitterMobile|nar_txnAmount|nar_txnCurrency|nar_version|nar_returnUrl
     * CRITICAL: nar_mcccode is EXCLUDED from source string but included in POST body
     */
    constructSourceString(data) {
        // CRITICAL: Exact field order and handling as per BSP manual
        const FIELDS = [
            'nar_cardType','nar_merBankCode','nar_merId','nar_merTxnTime','nar_msgType',
            'nar_orderNo','nar_paymentDesc','nar_remitterEmail','nar_remitterMobile',
            'nar_txnAmount','nar_txnCurrency','nar_version','nar_returnUrl'
        ];

        // Assert nar_mcccode is excluded from HMAC source string
        if (FIELDS.includes('nar_mcccode')) {
            throw new Error('nar_mcccode must be excluded from HMAC source string');
        }

        // Construct source string: no quotes, no trim, preserve empties, explicit string coercion
        const sourceString = FIELDS.map(field => (data[field] ?? '') + '').join('|');

        console.log(`[HMAC] Source string fields: ${FIELDS.length}`);
        console.log(`[HMAC] Source string length: ${sourceString.length}`);
        console.log(`[HMAC] Source string (first 120 chars): ${sourceString.substring(0, 120)}`);

        // Log the exact source string for debugging (mask sensitive parts)
        const maskedSourceString = sourceString.replace(
            /(nar_merId[^|]*\|[^|]*\|)([^|]{4})([^|]*)([^|]{4})/,
            '$1$2***$4'
        );
        console.log(`[HMAC] Masked source string: ${maskedSourceString}`);

        return sourceString;
    }

    /**
     * Calculate HMAC SHA-256 using Node.js crypto module
     * @param {string} message - Message to sign
     * @param {string} key - HMAC key (UTF-8 string, as per BSP sample code)
     * @returns {string} - Hex encoded signature (lowercase)
     */
    hmacSHA256(message, key) {
        try {
            // Convert UTF-8 key to Buffer
            const keyBuffer = Buffer.from(key, 'utf8');

            // Create HMAC
            const hmac = crypto.createHmac('sha256', keyBuffer);
            hmac.update(message, 'utf8');

            // Return hex digest (lowercase as per BSP manual)
            return hmac.digest('hex').toLowerCase();

        } catch (error) {
            console.error(`[HMAC] HMAC calculation error:`, error.message);
            throw new Error(`HMAC calculation failed: ${error.message}`);
        }
    }

    /**
     * Verify checksum for response validation
     * @param {Object} responseData - Response data from BSP IPG
     * @param {string} receivedChecksum - Checksum received from BSP
     * @returns {boolean} - Verification result
     */
    verifyResponseChecksum(responseData, receivedChecksum) {
        try {
            // Construct source string for AC message response
            const sourceString = this.constructResponseSourceString(responseData);
            const calculatedChecksum = this.hmacSHA256(sourceString, this.bspConfig.HMAC_KEY);
            
            const isValid = calculatedChecksum.toLowerCase() === receivedChecksum.toLowerCase();
            
            console.log(`[HMAC] Response checksum verification: ${isValid ? 'VALID' : 'INVALID'}`);
            
            if (!isValid) {
                console.log(`[HMAC] Expected: ${calculatedChecksum}`);
                console.log(`[HMAC] Received: ${receivedChecksum}`);
            }
            
            return isValid;
            
        } catch (error) {
            console.error(`[HMAC] Response checksum verification failed:`, error.message);
            return false;
        }
    }

    /**
     * Construct source string for AC response message
     * Format: nar_cardNo|nar_cardType|nar_debitAuthCode|nar_debitAuthNo|nar_merId|nar_merTxnTime|nar_msgType|nar_narTxnId|nar_narTxnTime|nar_orderNo|nar_remarks|nar_remitterBankId|nar_remitterName|nar_txnAmount|nar_txnCurrency
     */
    constructResponseSourceString(data) {
        const elements = [
            data.nar_cardNo || '',
            data.nar_cardType || '',
            data.nar_debitAuthCode || '',
            data.nar_debitAuthNo || '',
            data.nar_merId || '',
            data.nar_merTxnTime || '',
            data.nar_msgType || '',
            data.nar_narTxnId || '',
            data.nar_narTxnTime || '',
            data.nar_orderNo || '',
            data.nar_remarks || '',
            data.nar_remitterBankId || '',
            data.nar_remitterName || '',
            data.nar_txnAmount || '',
            data.nar_txnCurrency || ''
        ];

        const sourceString = elements.join('|');
        
        console.log(`[HMAC] Response source string constructed: ${sourceString.length} chars`);
        
        return sourceString;
    }

    /**
     * Validate checksum format
     */
    isValidChecksumFormat(checksum) {
        // HMAC SHA-256 produces 64 character hex string
        return typeof checksum === 'string' && 
               checksum.length === 64 && 
               /^[0-9a-fA-F]+$/.test(checksum);
    }
}

module.exports = new HMACService();
