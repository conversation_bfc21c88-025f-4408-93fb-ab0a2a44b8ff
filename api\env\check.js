/**
 * Environment Variables Check API
 * Verifies that all required BSP IPG environment variables are present
 * Returns status without exposing actual values for security
 */

module.exports = async (req, res) => {
    // Set CORS headers
    res.setHeader('Access-Control-Allow-Origin', process.env.CORS_ORIGIN || '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

    // Handle preflight requests
    if (req.method === 'OPTIONS') {
        return res.status(200).end();
    }

    // Only allow GET requests
    if (req.method !== 'GET') {
        return res.status(405).json({ 
            success: false, 
            error: 'Method not allowed' 
        });
    }

    try {
        // Define required environment variables (core BSP IPG functionality)
        const requiredVars = [
            'BSP_HMAC_KEY',
            'MERCHANT_ID',
            'MCC_CODE',
            'MERCHANT_NAME',
            'BSP_PAYMENT_URL',
            'BSP_PORTAL_URL',
            'RETURN_URL',
            'WEBSITE_ADDRESS',
            'NODE_ENV',
            'CORS_ORIGIN'
        ];

        // Optional variables (nice to have but not critical)
        const optionalVars = [
            'PORT',
            'RATE_LIMIT_WINDOW_MS',
            'RATE_LIMIT_MAX_REQUESTS',
            'BSP_ENV',
            'BSP_PAYMENT_DESCRIPTION',
            'DEFAULT_AMOUNT',
            'MIN_AMOUNT',
            'MAX_AMOUNT',
            'AMOUNT_DECIMAL_PLACES',
            'ENABLE_HMAC_TESTING',
            // BSP Merchant ID Components (for reference and flexibility)
            'MERCHANT_MID',
            'MERCHANT_SID',
            // UAT Environment variables (optional)
            'BSP_HMAC_KEY_UAT',
            'BSP_PAYMENT_URL_UAT',
            'BSP_PORTAL_URL_UAT',
            'MERCHANT_MID_UAT',
            'MERCHANT_SID_UAT',
            'MERCHANT_ID_UAT',
            'MCC_CODE_UAT'
        ];

        // Check each required variable
        const envStatus = {};
        let allRequiredPresent = true;
        let totalRequired = 0;
        let presentRequired = 0;

        // Check required variables
        requiredVars.forEach(varName => {
            const isPresent = !!process.env[varName];
            const hasValue = isPresent && process.env[varName].trim().length > 0;
            
            envStatus[varName] = {
                present: isPresent,
                hasValue: hasValue,
                required: true,
                status: hasValue ? 'OK' : (isPresent ? 'EMPTY' : 'MISSING')
            };

            totalRequired++;
            if (hasValue) {
                presentRequired++;
            } else {
                allRequiredPresent = false;
            }
        });

        // Check optional variables
        let totalOptional = 0;
        let presentOptional = 0;

        optionalVars.forEach(varName => {
            const isPresent = !!process.env[varName];
            const hasValue = isPresent && process.env[varName].trim().length > 0;
            
            envStatus[varName] = {
                present: isPresent,
                hasValue: hasValue,
                required: false,
                status: hasValue ? 'OK' : (isPresent ? 'EMPTY' : 'MISSING')
            };

            totalOptional++;
            if (hasValue) {
                presentOptional++;
            }
        });

        // Calculate overall status
        const overallStatus = allRequiredPresent ? 'PASS' : 'FAIL';
        const completionPercentage = Math.round((presentRequired / totalRequired) * 100);

        // Prepare response
        const response = {
            success: true,
            overallStatus,
            completionPercentage,
            summary: {
                required: {
                    total: totalRequired,
                    present: presentRequired,
                    missing: totalRequired - presentRequired
                },
                optional: {
                    total: totalOptional,
                    present: presentOptional,
                    missing: totalOptional - presentOptional
                }
            },
            variables: envStatus,
            environment: process.env.NODE_ENV || 'development',
            bspEnvironment: process.env.BSP_ENV || 'prod',
            timestamp: new Date().toISOString()
        };

        // Set appropriate HTTP status
        const httpStatus = allRequiredPresent ? 200 : 422; // 422 = Unprocessable Entity

        return res.status(httpStatus).json(response);

    } catch (error) {
        console.error('Environment check error:', error);
        
        return res.status(500).json({
            success: false,
            error: 'Internal server error during environment check',
            overallStatus: 'ERROR'
        });
    }
}
