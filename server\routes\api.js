/**
 * API Routes for BSP IPG Payment Processing
 * Provides secure endpoints for configuration and payment processing
 */

const express = require('express');
const { body, validationResult } = require('express-validator');
const config = require('../config/config');
const paymentService = require('../services/payment-service');
const hmacService = require('../services/hmac-service');

const router = express.Router();

/**
 * GET /api/config
 * Returns public configuration (no sensitive data)
 */
router.get('/config', (req, res) => {
    try {
        const publicConfig = config.getPublicConfig();
        
        console.log('[API] Public configuration requested');
        
        res.json({
            success: true,
            config: publicConfig
        });
        
    } catch (error) {
        console.error('[API] Config request failed:', error.message);
        res.status(500).json({
            success: false,
            error: 'Configuration not available'
        });
    }
});

/**
 * POST /api/payment/initiate
 * Initiates payment processing with secure HMAC calculation
 */
router.post('/payment/initiate', [
    // Validation middleware
    body('amount')
        .isFloat({ min: 0.01, max: 999999.99 })
        .withMessage('Amount must be between 0.01 and 999999.99'),
    body('amount')
        .custom((value) => {
            const decimalPlaces = (value.toString().split('.')[1] || '').length;
            if (decimalPlaces > 2) {
                throw new Error('Amount cannot have more than 2 decimal places');
            }
            return true;
        })
], async (req, res) => {
    try {
        // Check validation results
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                success: false,
                error: 'Validation failed',
                details: errors.array()
            });
        }

        const { amount } = req.body;
        
        console.log(`[API] Payment initiation requested for FJD ${amount}`);
        
        // Process payment through secure service
        const result = await paymentService.initiatePayment(amount);
        
        res.json(result);
        
    } catch (error) {
        console.error('[API] Payment initiation failed:', error.message);
        res.status(400).json({
            success: false,
            error: error.message
        });
    }
});

/**
 * POST /api/payment/verify
 * Verifies payment response from BSP IPG
 */
router.post('/payment/verify', [
    // Basic validation for required fields
    body('nar_orderNo').notEmpty().withMessage('Order number is required'),
    body('nar_msgType').notEmpty().withMessage('Message type is required')
], (req, res) => {
    try {
        // Check validation results
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                success: false,
                error: 'Validation failed',
                details: errors.array()
            });
        }

        const responseData = req.body;
        
        console.log(`[API] Payment verification requested for order: ${responseData.nar_orderNo}`);
        
        // Verify payment response
        const result = paymentService.verifyPaymentResponse(responseData);
        
        res.json(result);
        
    } catch (error) {
        console.error('[API] Payment verification failed:', error.message);
        res.status(400).json({
            success: false,
            error: error.message
        });
    }
});

/**
 * POST /api/hmac/test
 * Test HMAC calculation (for development/testing only)
 */
router.post('/hmac/test', [
    body('message').notEmpty().withMessage('Message is required')
], (req, res) => {
    try {
        // Only allow in development mode
        if (process.env.NODE_ENV === 'production') {
            return res.status(403).json({
                success: false,
                error: 'HMAC testing not available in production'
            });
        }

        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                success: false,
                error: 'Validation failed',
                details: errors.array()
            });
        }

        const { message, testData } = req.body;
        
        console.log('[API] HMAC test requested');
        
        let result = {};
        
        if (testData) {
            // Replace placeholders with actual values from .env file
            const bspConfig = config.getBSPConfig();
            const actualTestData = { ...testData };

            // CRITICAL FIX: Handle both SERVER_MERCHANT_ID and PRODUCTION_MERCHANT_ID placeholders
            if (actualTestData.nar_merId === 'SERVER_MERCHANT_ID' || actualTestData.nar_merId === 'PRODUCTION_MERCHANT_ID') {
                actualTestData.nar_merId = bspConfig.MERCHANT_ID; // Uses MERCHANT_ID=800126108001016 from .env
                console.log(`[API] Replaced placeholder ${testData.nar_merId} with actual MERCHANT_ID: ${bspConfig.MERCHANT_ID}`);
            }

            // CRITICAL FIX: Handle both SERVER_MCC_CODE and PRODUCTION_MCC_CODE placeholders
            if (actualTestData.nar_mcccode === 'SERVER_MCC_CODE' || actualTestData.nar_mcccode === 'PRODUCTION_MCC_CODE') {
                actualTestData.nar_mcccode = bspConfig.MCC_CODE; // Uses MCC_CODE=8398 from .env
                console.log(`[API] Replaced placeholder ${testData.nar_mcccode} with actual MCC_CODE: ${bspConfig.MCC_CODE}`);
            }

            // Fix return URL placeholder replacement
            if (actualTestData.nar_returnUrl && actualTestData.nar_returnUrl.includes('example.com')) {
                actualTestData.nar_returnUrl = bspConfig.RETURN_URL;
                console.log(`[API] Replaced example.com with actual RETURN_URL: ${bspConfig.RETURN_URL}`);
            }

            // Test with payment data structure using actual .env values
            const sourceString = hmacService.constructSourceString(actualTestData);
            const checksum = hmacService.calculateChecksum(actualTestData);

            result = {
                success: true,
                sourceString: sourceString,
                checksum: checksum,
                checksumLength: checksum.length
            };
        } else {
            // Test with simple message
            const bspConfig = config.getBSPConfig();
            const checksum = hmacService.hmacSHA256(message, bspConfig.HMAC_KEY);
            
            result = {
                success: true,
                message: message,
                checksum: checksum,
                checksumLength: checksum.length
            };
        }
        
        res.json(result);
        
    } catch (error) {
        console.error('[API] HMAC test failed:', error.message);
        res.status(400).json({
            success: false,
            error: error.message
        });
    }
});

/**
 * GET /api/health
 * Health check endpoint
 */
router.get('/health', (req, res) => {
    try {
        const bspConfig = config.getBSPConfig();
        const serverConfig = config.getServerConfig();
        
        const health = {
            status: 'healthy',
            timestamp: new Date().toISOString(),
            environment: serverConfig.nodeEnv,
            services: {
                config: !!bspConfig.HMAC_KEY,
                hmac: true,
                payment: true
            }
        };
        
        res.json(health);
        
    } catch (error) {
        res.status(500).json({
            status: 'unhealthy',
            error: error.message
        });
    }
});

module.exports = router;
