/**
 * Vercel Serverless Function: GET /api/health
 * Health check endpoint
 */

const config = require('../server/config/config');

module.exports = async (req, res) => {
    // Set CORS headers
    res.setHeader('Access-Control-Allow-Credentials', true);
    res.setHeader('Access-Control-Allow-Origin', process.env.CORS_ORIGIN || '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET,OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

    // Handle preflight requests
    if (req.method === 'OPTIONS') {
        res.status(200).end();
        return;
    }

    // Only allow GET requests
    if (req.method !== 'GET') {
        return res.status(405).json({
            success: false,
            error: 'Method not allowed'
        });
    }

    try {
        const bspConfig = config.getBSPConfig();
        const serverConfig = config.getServerConfig();
        
        const health = {
            status: 'healthy',
            timestamp: new Date().toISOString(),
            environment: serverConfig.nodeEnv,
            platform: 'vercel',
            services: {
                config: !!bspConfig.HMAC_KEY,
                hmac: true,
                payment: true
            }
        };
        
        res.status(200).json(health);
        
    } catch (error) {
        res.status(500).json({
            status: 'unhealthy',
            error: error.message,
            platform: 'vercel'
        });
    }
};
