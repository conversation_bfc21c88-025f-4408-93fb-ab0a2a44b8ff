/**
 * Vercel Serverless Function: POST /api/hmac/test
 * Test HMAC calculation (for development/testing only)
 */

const config = require('../../server/config/config');
const hmacService = require('../../server/services/hmac-service');

module.exports = async (req, res) => {
    // Set CORS headers
    res.setHeader('Access-Control-Allow-Credentials', true);
    res.setHeader('Access-Control-Allow-Origin', process.env.CORS_ORIGIN || '*');
    res.setHeader('Access-Control-Allow-Methods', 'POST,OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

    // Handle preflight requests
    if (req.method === 'OPTIONS') {
        res.status(200).end();
        return;
    }

    // Only allow POST requests
    if (req.method !== 'POST') {
        return res.status(405).json({
            success: false,
            error: 'Method not allowed'
        });
    }

    try {
        // Allow HMAC testing in development or when explicitly enabled
        const testingEnabled = process.env.NODE_ENV !== 'production' || process.env.ENABLE_HMAC_TESTING === 'true';

        if (!testingEnabled) {
            return res.status(403).json({
                success: false,
                error: 'HMAC testing not available in production. Set ENABLE_HMAC_TESTING=true to enable.'
            });
        }

        const { message, testData } = req.body;
        
        if (!message && !testData) {
            return res.status(400).json({
                success: false,
                error: 'Message or test data is required'
            });
        }
        
        console.log('[API] HMAC test requested');
        
        let result = {};
        
        if (testData) {
            // Replace placeholders with actual values from .env file
            const bspConfig = config.getBSPConfig();
            const actualTestData = { ...testData };

            // CRITICAL FIX: Handle both SERVER_MERCHANT_ID and PRODUCTION_MERCHANT_ID placeholders
            if (actualTestData.nar_merId === 'SERVER_MERCHANT_ID' || actualTestData.nar_merId === 'PRODUCTION_MERCHANT_ID') {
                actualTestData.nar_merId = bspConfig.MERCHANT_ID; // Uses MERCHANT_ID=800126108001016 from .env
                console.log(`[API] Replaced placeholder ${testData.nar_merId} with actual MERCHANT_ID: ${bspConfig.MERCHANT_ID}`);
            }

            // CRITICAL FIX: Handle both SERVER_MCC_CODE and PRODUCTION_MCC_CODE placeholders
            if (actualTestData.nar_mcccode === 'SERVER_MCC_CODE' || actualTestData.nar_mcccode === 'PRODUCTION_MCC_CODE') {
                actualTestData.nar_mcccode = bspConfig.MCC_CODE; // Uses MCC_CODE=8398 from .env
                console.log(`[API] Replaced placeholder ${testData.nar_mcccode} with actual MCC_CODE: ${bspConfig.MCC_CODE}`);
            }

            // Fix return URL placeholder replacement
            if (actualTestData.nar_returnUrl && actualTestData.nar_returnUrl.includes('example.com')) {
                actualTestData.nar_returnUrl = bspConfig.RETURN_URL;
                console.log(`[API] Replaced example.com with actual RETURN_URL: ${bspConfig.RETURN_URL}`);
            }

            // Test with payment data structure using actual .env values
            const sourceString = hmacService.constructSourceString(actualTestData);
            const checksum = hmacService.calculateChecksum(actualTestData);

            result = {
                success: true,
                sourceString: sourceString,
                checksum: checksum,
                checksumLength: checksum.length
            };
        } else {
            // Test with simple message
            const bspConfig = config.getBSPConfig();
            const checksum = hmacService.hmacSHA256(message, bspConfig.HMAC_KEY);
            
            result = {
                success: true,
                message: message,
                checksum: checksum,
                checksumLength: checksum.length
            };
        }
        
        res.status(200).json(result);
        
    } catch (error) {
        console.error('[API] HMAC test failed:', error.message);
        res.status(400).json({
            success: false,
            error: error.message
        });
    }
};
