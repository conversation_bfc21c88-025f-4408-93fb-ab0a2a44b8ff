/**
 * Comprehensive Error Handler for BSP IPG Payment Testing
 * Provides centralized error handling and user-friendly error messages
 * Based on BSP IPG Manual v2.54 error codes
 */

class ErrorHandler {
    constructor() {
        this.debugService = null;
        this.errorCodes = this.initializeErrorCodes();
    }

    /**
     * Initialize error codes based on BSP IPG manual
     */
    initializeErrorCodes() {
        return {
            // BSP IPG Response Codes
            '00': 'Transaction Approved',
            '01': 'Refer to Card Issuer',
            '02': 'Refer to Card Issuer (Special Condition)',
            '03': 'Invalid Merchant',
            '04': 'Pick Up Card',
            '05': 'Do Not Honor',
            '06': 'Error',
            '07': 'Pick Up Card (Special Condition)',
            '08': 'Honor with Identification',
            '09': 'Request in Progress',
            '10': 'Approved for Partial Amount',
            '11': 'Approved (VIP)',
            '12': 'Invalid Transaction',
            '13': 'Invalid Amount',
            '14': 'Invalid Card Number',
            '15': 'No Such Issuer',
            '16': 'Approved, Update Track 3',
            '17': 'Customer Cancellation',
            '18': 'Customer Dispute',
            '19': 'Re-enter Transaction',
            '20': 'Invalid Response',
            '21': 'No Action Taken',
            '22': 'Suspected Malfunction',
            '23': 'Unacceptable Transaction Fee',
            '24': 'File Update Not Supported',
            '25': 'Unable to Locate Record',
            '26': 'Duplicate File Update Record',
            '27': 'File Update Field Edit Error',
            '28': 'File Update File Locked Out',
            '29': 'File Update Not Successful',
            '30': 'Format Error',
            '31': 'Bank Not Supported by Switch',
            '32': 'Completed Partially',
            '33': 'Expired Card',
            '34': 'Suspected Fraud',
            '35': 'Card Acceptor Contact Acquirer',
            '36': 'Restricted Card',
            '37': 'Card Acceptor Call Acquirer Security',
            '38': 'Allowable PIN Tries Exceeded',
            '39': 'No Credit Account',
            '40': 'Requested Function Not Supported',
            '41': 'Lost Card',
            '42': 'No Universal Account',
            '43': 'Stolen Card',
            '44': 'No Investment Account',
            '51': 'Insufficient Funds',
            '52': 'No Checking Account',
            '53': 'No Savings Account',
            '54': 'Expired Card',
            '55': 'Incorrect PIN',
            '56': 'No Card Record',
            '57': 'Transaction Not Permitted to Cardholder',
            '58': 'Transaction Not Permitted to Terminal',
            '59': 'Suspected Fraud',
            '60': 'Card Acceptor Contact Acquirer',
            '61': 'Exceeds Withdrawal Amount Limit',
            '62': 'Restricted Card',
            '63': 'Security Violation',
            '64': 'Original Amount Incorrect',
            '65': 'Exceeds Withdrawal Frequency Limit',
            '66': 'Card Acceptor Call Acquirer Security',
            '67': 'Hard Capture',
            '68': 'Response Received Too Late',
            '75': 'Allowable Number of PIN Tries Exceeded',
            '90': 'Cutoff is in Process',
            '91': 'Issuer Unavailable',
            '92': 'Financial Institution Not Found',
            '93': 'Transaction Cannot be Completed',
            '94': 'Duplicate Transmission',
            '95': 'Reconcile Error',
            '96': 'System Malfunction',
            '97': 'Reserved for National Use',
            '98': 'Reserved for National Use',
            '99': 'Reserved for National Use',
            
            // BSP IPG specific codes from manual
            'UN': 'Unknown error',
            'TO': 'Session Timeout at BSP Secure Entry Page',
            'NF': 'Transaction Not Found',
            'IM': 'Invalid Message',
            'N7': 'Invalid CVV2',
            
            // Custom application error codes
            'CONFIG_ERROR': 'Configuration validation failed',
            'NETWORK_ERROR': 'Network connection error',
            'TIMEOUT_ERROR': 'Request timeout',
            'VALIDATION_ERROR': 'Data validation failed',
            'CHECKSUM_ERROR': 'Checksum calculation or verification failed',
            'FORM_ERROR': 'Form submission error',
            'INIT_ERROR': 'Application initialization error'
        };
    }

    /**
     * Set debug service reference
     */
    setDebugService(debugService) {
        this.debugService = debugService;
    }

    /**
     * Handle application errors with appropriate user messaging
     */
    handleError(error, context = '', showToUser = true) {
        const errorInfo = this.analyzeError(error, context);
        
        // Log to debug console
        if (this.debugService) {
            this.debugService.log('error', `${context}: ${errorInfo.message}`);
            if (errorInfo.details) {
                this.debugService.log('error', `Details: ${errorInfo.details}`);
            }
            if (errorInfo.suggestion) {
                this.debugService.log('info', `Suggestion: ${errorInfo.suggestion}`);
            }
        }

        // Log to browser console
        console.error(`[BSP IPG Error] ${context}:`, error);

        // Show user-friendly message if requested
        if (showToUser) {
            this.showUserError(errorInfo);
        }

        return errorInfo;
    }

    /**
     * Analyze error and provide structured information
     */
    analyzeError(error, context) {
        let errorInfo = {
            code: 'UNKNOWN',
            message: 'An unknown error occurred',
            details: null,
            suggestion: null,
            severity: 'error'
        };

        if (typeof error === 'string') {
            errorInfo.message = error;
        } else if (error instanceof Error) {
            errorInfo.message = error.message;
            errorInfo.details = error.stack;
        } else if (typeof error === 'object' && error.message) {
            errorInfo.message = error.message;
            errorInfo.code = error.code || 'UNKNOWN';
        }

        // Analyze specific error types
        if (context.includes('config') || errorInfo.message.includes('config')) {
            errorInfo.code = 'CONFIG_ERROR';
            errorInfo.suggestion = 'Check that all required configuration values are present in config.js';
        } else if (context.includes('network') || errorInfo.message.includes('fetch')) {
            errorInfo.code = 'NETWORK_ERROR';
            errorInfo.suggestion = 'Check your internet connection and try again';
        } else if (context.includes('checksum') || errorInfo.message.includes('HMAC')) {
            errorInfo.code = 'CHECKSUM_ERROR';
            errorInfo.suggestion = 'Verify HMAC key configuration and data integrity';
        } else if (context.includes('form') || errorInfo.message.includes('form')) {
            errorInfo.code = 'FORM_ERROR';
            errorInfo.suggestion = 'Check that all required form fields are present';
        } else if (context.includes('timeout')) {
            errorInfo.code = 'TIMEOUT_ERROR';
            errorInfo.suggestion = 'The request timed out. Please try again';
        } else if (errorInfo.message.includes('amount')) {
            errorInfo.code = 'VALIDATION_ERROR';
            errorInfo.suggestion = 'Please enter a valid amount between FJD 0.01 and FJD 999,999.99';
        }

        return errorInfo;
    }

    /**
     * Handle BSP IPG response errors
     */
    handlePaymentError(responseCode, remarks = '') {
        const errorMessage = this.errorCodes[responseCode] || `Unknown error code: ${responseCode}`;
        
        const errorInfo = {
            code: responseCode,
            message: errorMessage,
            details: remarks,
            suggestion: this.getPaymentErrorSuggestion(responseCode),
            severity: this.getErrorSeverity(responseCode)
        };

        if (this.debugService) {
            this.debugService.log('error', `Payment failed with code ${responseCode}: ${errorMessage}`);
            if (remarks) {
                this.debugService.log('error', `Remarks: ${remarks}`);
            }
            if (errorInfo.suggestion) {
                this.debugService.log('info', `Suggestion: ${errorInfo.suggestion}`);
            }
        }

        return errorInfo;
    }

    /**
     * Get suggestion for payment error codes
     */
    getPaymentErrorSuggestion(code) {
        const suggestions = {
            '01': 'Please contact your card issuer',
            '03': 'Invalid merchant configuration - contact support',
            '05': 'Transaction declined by bank - try a different card',
            '12': 'Invalid transaction - check payment details',
            '13': 'Invalid amount - check the payment amount',
            '14': 'Invalid card number - check card details',
            '33': 'Card has expired - use a valid card',
            '34': 'Suspected fraud - contact your bank',
            '41': 'Card reported as lost - contact your bank',
            '43': 'Card reported as stolen - contact your bank',
            '51': 'Insufficient funds - check your account balance',
            '54': 'Card has expired - use a valid card',
            '55': 'Incorrect PIN - try again with correct PIN',
            '57': 'Transaction not permitted - contact your bank',
            '91': 'Bank system unavailable - try again later',
            'TO': 'Session timed out - please start the payment process again',
            'N7': 'Invalid CVV - check the security code on your card'
        };

        return suggestions[code] || 'Please try again or contact support if the problem persists';
    }

    /**
     * Get error severity level
     */
    getErrorSeverity(code) {
        const criticalCodes = ['03', '96', '97', 'UN', 'IM'];
        const warningCodes = ['TO', '91', '92'];
        
        if (criticalCodes.includes(code)) {
            return 'critical';
        } else if (warningCodes.includes(code)) {
            return 'warning';
        } else {
            return 'error';
        }
    }

    /**
     * Show user-friendly error message
     */
    showUserError(errorInfo) {
        let message = errorInfo.message;
        
        if (errorInfo.suggestion) {
            message += `\n\nSuggestion: ${errorInfo.suggestion}`;
        }

        // In a production app, you might want to use a modal or toast notification
        alert(`Error: ${message}`);
    }

    /**
     * Validate payment data before submission
     */
    validatePaymentData(paymentData) {
        const errors = [];
        const required = [
            'nar_msgType', 'nar_merTxnTime', 'nar_orderNo', 'nar_merId',
            'nar_txnCurrency', 'nar_txnAmount', 'nar_paymentDesc',
            'nar_returnUrl', 'nar_mcccode', 'nar_Secure'
        ];

        required.forEach(field => {
            if (!paymentData[field]) {
                errors.push(`Missing required field: ${field}`);
            }
        });

        // Validate amount
        const amount = parseFloat(paymentData.nar_txnAmount);
        if (isNaN(amount) || amount <= 0) {
            errors.push('Invalid transaction amount');
        }

        // Validate currency
        if (paymentData.nar_txnCurrency !== '242') {
            errors.push('Invalid currency code (must be 242 for FJD)');
        }

        // Validate merchant ID format
        if (paymentData.nar_merId && paymentData.nar_merId.length !== 15) {
            errors.push('Invalid merchant ID format');
        }

        if (errors.length > 0) {
            const error = new Error(`Payment data validation failed: ${errors.join(', ')}`);
            error.code = 'VALIDATION_ERROR';
            throw error;
        }

        return true;
    }

    /**
     * Get error code description
     */
    getErrorDescription(code) {
        return this.errorCodes[code] || `Unknown error code: ${code}`;
    }
}

// Create global instance
window.errorHandler = new ErrorHandler();
