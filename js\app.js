/**
 * Main Application Controller for BSP IPG Payment Testing
 * Initializes all services and handles UI interactions
 * Coordinates the complete payment flow
 */

class PaymentApp {
    constructor() {
        this.isInitialized = false;
        this.services = {
            config: null,
            debug: null,
            hmac: null,
            payment: null,
            error: null
        };
    }

    /**
     * Initialize the secure application
     */
    async init() {
        try {
            // Initialize debug service first
            const debugConsole = document.getElementById('debugConsole');
            if (!debugConsole) {
                throw new Error('Debug console element not found');
            }

            debugService.init(debugConsole);
            debugService.log('info', '🚀 BSP IPG Secure Payment Application starting...');
            debugService.log('info', '🔒 Loading configuration from secure server...');
            this.services.debug = debugService;

            // Initialize error handler
            errorHandler.setDebugService(debugService);
            this.services.error = errorHandler;

            // Load configuration from secure server
            try {
                await configService.loadConfig();
                configService.validateConfig();
                debugService.logConfigValidation(true);
                debugService.log('success', '✅ Secure configuration loaded successfully');
                this.services.config = configService;
            } catch (error) {
                debugService.logConfigValidation(false, [error.message]);
                debugService.log('error', '❌ Failed to load secure configuration');
                errorHandler.handleError(error, 'Secure configuration loading');
                throw error;
            }

            // Initialize HMAC service
            hmacService.setDebugService(debugService);
            this.services.hmac = hmacService;

            // Initialize payment service
            paymentService.init(debugService, hmacService, errorHandler);
            this.services.payment = paymentService;

            // Setup UI event listeners
            this.setupEventListeners();

            // Log application ready
            debugService.log('success', '🎉 Secure application initialized successfully');
            debugService.log('info', '🔐 All sensitive operations handled server-side');
            this.logEnvironmentInfo();

            this.isInitialized = true;

        } catch (error) {
            console.error('Application initialization failed:', error);
            if (debugService) {
                debugService.logError(error, 'Application initialization failed');
            }
            if (errorHandler) {
                errorHandler.handleError(error, 'Application initialization', true);
            } else {
                this.showError('Application failed to initialize. Please check the console for details.');
            }
        }
    }

    /**
     * Setup UI event listeners
     */
    setupEventListeners() {
        // Pay button click handler
        const payButton = document.getElementById('payButton');
        if (payButton) {
            payButton.addEventListener('click', (e) => this.handlePayButtonClick(e));
        }

        // Clear debug console button
        const clearButton = document.getElementById('clearDebug');
        if (clearButton) {
            clearButton.addEventListener('click', () => {
                debugService.clear();
            });
        }

        // Amount input validation
        const amountInput = document.getElementById('amount');
        if (amountInput) {
            amountInput.addEventListener('input', (e) => this.validateAmountInput(e));
            amountInput.addEventListener('blur', (e) => this.formatAmountInput(e));
        }

        // Handle page visibility change (for debugging payment flow)
        document.addEventListener('visibilitychange', () => {
            if (!document.hidden && this.isInitialized) {
                debugService.log('info', 'Page became visible - user may have returned from payment gateway');
            }
        });

        debugService.log('info', 'Event listeners configured');
    }

    /**
     * Handle pay button click
     */
    async handlePayButtonClick(event) {
        event.preventDefault();

        if (!this.isInitialized) {
            this.showError('Application not properly initialized');
            return;
        }

        try {
            // Get amount from input
            const amountInput = document.getElementById('amount');
            const amount = parseFloat(amountInput.value);

            // Validate amount using comprehensive validation
            try {
                if (window.configService) {
                    window.configService.validateAmount(amount.toString());
                } else if (!this.isValidAmount(amount)) {
                    throw new Error('Invalid amount');
                }
            } catch (error) {
                this.showError(`Invalid amount: ${error.message}`);
                return;
            }

            // Disable button during processing
            const payButton = document.getElementById('payButton');
            const originalText = payButton.textContent;
            payButton.disabled = true;
            payButton.textContent = 'Processing...';

            debugService.log('info', `User initiated payment for FJD ${amount.toFixed(2)}`);

            // Process payment
            await paymentService.processPayment(amount);

        } catch (error) {
            debugService.logError(error, 'Payment initiation failed');
            const errorInfo = errorHandler.handleError(error, 'Payment initiation', false);
            this.showError(`Payment failed: ${errorInfo.message}`);

            // Re-enable button
            const payButton = document.getElementById('payButton');
            payButton.disabled = false;
            payButton.textContent = 'Donate Now via BSP IPG';

            paymentService.resetProcessing();
        }
    }

    /**
     * Validate amount input in real-time
     */
    validateAmountInput(event) {
        const input = event.target;
        const value = input.value;

        // Remove any non-numeric characters except decimal point
        const cleaned = value.replace(/[^0-9.]/g, '');
        
        // Ensure only one decimal point
        const parts = cleaned.split('.');
        if (parts.length > 2) {
            input.value = parts[0] + '.' + parts.slice(1).join('');
        } else {
            input.value = cleaned;
        }

        // Limit to 2 decimal places
        if (parts[1] && parts[1].length > 2) {
            input.value = parts[0] + '.' + parts[1].substring(0, 2);
        }
    }

    /**
     * Format amount on blur
     */
    formatAmountInput(event) {
        const input = event.target;
        const value = parseFloat(input.value);
        
        if (!isNaN(value) && value > 0) {
            input.value = value.toFixed(BSP_CONFIG.decimalPlaces || 2);
        } else {
            input.value = BSP_CONFIG.defaultAmount || '1.00';
        }
    }

    /**
     * Check if amount is valid
     */
    isValidAmount(amount) {
        try {
            // Use the comprehensive validation from config service
            if (window.configService) {
                window.configService.validateAmount(amount.toString());
                return true;
            } else {
                // Fallback validation if config service not available
                return !isNaN(amount) &&
                       amount > 0 &&
                       amount >= (BSP_CONFIG.minAmount || 0.01) &&
                       amount <= (BSP_CONFIG.maxAmount || 999999.99);
            }
        } catch (error) {
            return false;
        }
    }

    /**
     * Log environment information
     */
    logEnvironmentInfo() {
        const envInfo = configService.getEnvironmentInfo();
        Object.entries(envInfo).forEach(([key, value]) => {
            debugService.log('info', `${key}: ${value}`);
        });
    }

    /**
     * Show error message to user
     */
    showError(message) {
        // Log to debug console
        debugService.log('error', message);
        
        // Show browser alert (in production, you might want a better UI)
        alert(`Error: ${message}`);
    }

    /**
     * Show success message to user
     */
    showSuccess(message) {
        debugService.log('success', message);
        // Could add a success notification UI here
    }

    /**
     * Handle application errors
     */
    handleError(error, context = '') {
        console.error(`[PaymentApp] ${context}:`, error);
        debugService.logError(error, context);
    }

    /**
     * Get application status
     */
    getStatus() {
        return {
            initialized: this.isInitialized,
            services: {
                config: !!this.services.config,
                debug: !!this.services.debug,
                hmac: !!this.services.hmac,
                payment: !!this.services.payment,
                error: !!this.services.error
            },
            config: this.services.config ? this.services.config.getConfigSummary() : null,
            payment: this.services.payment ? this.services.payment.getStatus() : null
        };
    }

    /**
     * Perform health check
     */
    healthCheck() {
        const status = this.getStatus();
        const issues = [];

        if (!status.initialized) {
            issues.push('Application not initialized');
        }

        Object.entries(status.services).forEach(([service, available]) => {
            if (!available) {
                issues.push(`${service} service not available`);
            }
        });

        if (!window.crypto?.subtle) {
            issues.push('Web Crypto API not supported');
        }

        return {
            healthy: issues.length === 0,
            issues: issues,
            status: status
        };
    }
}

// Initialize application when DOM is loaded
document.addEventListener('DOMContentLoaded', async () => {
    window.paymentApp = new PaymentApp();
    await paymentApp.init();
});

// Handle page unload
window.addEventListener('beforeunload', (event) => {
    if (paymentService && paymentService.isProcessing) {
        event.preventDefault();
        event.returnValue = 'Payment is in progress. Are you sure you want to leave?';
        return event.returnValue;
    }
});

// Global error handler
window.addEventListener('error', (event) => {
    if (debugService) {
        debugService.logError(event.error, 'Global error');
    }
    console.error('Global error:', event.error);
});

// Handle unhandled promise rejections
window.addEventListener('unhandledrejection', (event) => {
    if (debugService) {
        debugService.logError(event.reason, 'Unhandled promise rejection');
    }
    console.error('Unhandled promise rejection:', event.reason);
});

// Expose health check for debugging
window.healthCheck = () => {
    if (window.paymentApp) {
        return window.paymentApp.healthCheck();
    }
    return { healthy: false, issues: ['Application not initialized'] };
};
