# Deployment Guide - SPCA Fiji BSP IPG Integration

## 🚀 Deployment Overview

This guide covers deploying the SPCA Fiji donation platform with BSP IPG integration to Vercel, including environment configuration, domain setup, and production readiness verification.

## 📋 Prerequisites

### **Required Accounts & Access**
- ✅ **Vercel Account**: For serverless deployment
- ✅ **GitHub Repository**: Source code access
- ✅ **BSP IPG Credentials**: Production HMAC key and merchant details
- ✅ **Domain Access**: DNS configuration for custom domain

### **Required Information**
- **BSP HMAC Key**: Production cryptographic key
- **Merchant ID**: 15-character BSP merchant identifier
- **MCC Code**: 4-digit merchant category code
- **Custom Domain**: Target domain for deployment

## 🔧 Environment Configuration

### **Step 1: Prepare Environment Variables**

Create a secure list of all required environment variables:

#### **Production BSP IPG Credentials**
```bash
BSP_HMAC_KEY=your_production_hmac_key_here
MERCHANT_MID=your_merchant_mid_here
MERCHANT_SID=your_merchant_sid_here
MERCHANT_ID=your_merchant_id_here
MCC_CODE=your_mcc_code_here
MERCHANT_NAME=Society for the Prevention of Cruelty to Animals (SPCA) Fiji Islands
```

#### **Production URLs**
```bash
BSP_PAYMENT_URL=https://oceania.thecardservicesonline.com/bsppg/mercpg
BSP_PORTAL_URL=https://oceania.thecardservicesonline.com/merchantportal/bsp/html
RETURN_URL=https://your-domain.com/thank-you
WEBSITE_ADDRESS=https://your-domain.com
```

#### **Server Configuration**
```bash
NODE_ENV=production
CORS_ORIGIN=https://your-domain.com
```

#### **Security Configuration (Optional)**
```bash
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
```

#### **Business Configuration (Optional)**
```bash
BSP_ENV=prod
BSP_PAYMENT_DESCRIPTION=SPCA Fiji Donation
DEFAULT_AMOUNT=1.00
MIN_AMOUNT=0.01
MAX_AMOUNT=999999.99
AMOUNT_DECIMAL_PLACES=2
ENABLE_HMAC_TESTING=true
```

### **Step 2: Validate Environment Variables**

Use the provided template to ensure all variables are correctly formatted:

**Reference**: `server/.env.example`
- Copy template values
- Replace placeholders with actual production values
- Verify HMAC key format (hex string)
- Confirm merchant ID length (15 characters)

## 🌐 Vercel Deployment

### **Step 1: Connect Repository**

1. **Login to Vercel**: https://vercel.com
2. **Import Project**: Click "New Project"
3. **Connect GitHub**: Authorize Vercel to access repository
4. **Select Repository**: Choose `donations.spcafiji.com`

### **Step 2: Configure Build Settings**

Vercel will auto-detect the project configuration:

```json
{
  "buildCommand": "npm run build",
  "outputDirectory": "dist",
  "installCommand": "npm install",
  "devCommand": "npm run dev"
}
```

**Note**: Vercel automatically handles Node.js serverless functions in the `/api` directory.

### **Step 3: Environment Variables Setup**

In Vercel Dashboard:

1. **Go to Project Settings** → **Environment Variables**
2. **Add each variable** from your prepared list
3. **Set Environment**: 
   - ✅ **Production** (for live deployment)
   - ✅ **Preview** (for staging/testing)
   - ⚠️ **Development** (optional, for local development)

#### **Critical Variables (Must Set)**
- `BSP_HMAC_KEY`
- `MERCHANT_ID`
- `MCC_CODE`
- `BSP_PAYMENT_URL`
- `RETURN_URL`
- `WEBSITE_ADDRESS`
- `NODE_ENV=production`
- `CORS_ORIGIN`

### **Step 4: Deploy**

1. **Click Deploy**: Vercel will build and deploy automatically
2. **Monitor Build**: Check build logs for any errors
3. **Verify Deployment**: Test the generated Vercel URL

## 🌍 Custom Domain Setup

### **Step 1: Add Domain in Vercel**

1. **Go to Project Settings** → **Domains**
2. **Add Domain**: Enter your custom domain (e.g., `donations.spcafiji.com`)
3. **Choose Configuration**: 
   - **Redirect**: Redirect www to non-www (or vice versa)
   - **Primary**: Set as primary domain

### **Step 2: DNS Configuration**

Configure DNS records with your domain provider:

#### **For Root Domain (donations.spcafiji.com)**
```
Type: A
Name: @
Value: ***********
```

#### **For WWW Subdomain (www.donations.spcafiji.com)**
```
Type: CNAME
Name: www
Value: cname.vercel-dns.com
```

#### **Alternative: CNAME for Root Domain**
```
Type: CNAME
Name: @
Value: cname.vercel-dns.com
```

### **Step 3: SSL Certificate**

Vercel automatically provisions SSL certificates:
- **Let's Encrypt**: Free SSL certificate
- **Auto-renewal**: Certificates renew automatically
- **HTTPS Redirect**: Automatic HTTP to HTTPS redirect

### **Step 4: Update Environment Variables**

Update URLs in Vercel environment variables:
```bash
RETURN_URL=https://donations.spcafiji.com/thank-you
WEBSITE_ADDRESS=https://donations.spcafiji.com
CORS_ORIGIN=https://donations.spcafiji.com
```

## ✅ Production Verification

### **Step 1: Environment Health Check**

Visit: `https://your-domain.com/api/env/check`

Expected response:
```json
{
  "status": "healthy",
  "environment": "production",
  "requiredVars": {
    "BSP_HMAC_KEY": "✅ Set",
    "MERCHANT_ID": "✅ Set (15 chars)",
    "MCC_CODE": "✅ Set"
  },
  "timestamp": "2024-12-17T12:00:00.000Z"
}
```

### **Step 2: HMAC Testing**

Visit: `https://your-domain.com/test-hmac.html`

Verify:
- ✅ **Source String Construction**: Shows correct field order
- ✅ **Generated Checksum**: 64-character hex string
- ✅ **Production Values**: Uses actual merchant ID and credentials

### **Step 3: Payment Form Testing**

Visit: `https://your-domain.com`

Test:
- ✅ **Form Loads**: Donation form displays correctly
- ✅ **Validation**: Input validation works
- ✅ **Submission**: Form submits to BSP IPG
- ✅ **Redirect**: Proper redirect to BSP payment gateway

### **Step 4: End-to-End Testing**

**Complete Payment Flow:**
1. **Fill Form**: Enter test donation amount
2. **Submit**: Click "Donate Now"
3. **BSP Gateway**: Redirected to BSP IPG
4. **Payment**: Complete test payment (if available)
5. **Return**: Redirected back to thank you page

## 🔒 Security Checklist

### **Environment Security**
- ✅ **HMAC Key**: Never exposed to client-side code
- ✅ **HTTPS Only**: All communications encrypted
- ✅ **Environment Variables**: Stored securely in Vercel
- ✅ **No Hardcoded Secrets**: All credentials in environment variables

### **Application Security**
- ✅ **CORS Configuration**: Proper cross-origin settings
- ✅ **Rate Limiting**: Protection against abuse
- ✅ **Input Validation**: Server-side validation
- ✅ **Error Handling**: No sensitive information in error messages

### **BSP IPG Security**
- ✅ **HMAC SHA-256**: Proper cryptographic signing
- ✅ **Source String**: Correct field construction
- ✅ **Referrer Policy**: Proper referrer headers
- ✅ **Field Validation**: All BSP requirements met

## 🔄 Continuous Deployment

### **Automatic Deployment**

Vercel automatically deploys when:
- **Main Branch**: Push to main branch triggers production deployment
- **Feature Branches**: Push to other branches creates preview deployments
- **Pull Requests**: Automatic preview deployments for review

### **Deployment Monitoring**

Monitor deployments via:
- **Vercel Dashboard**: Build logs and deployment status
- **GitHub Integration**: Deployment status in pull requests
- **Webhook Notifications**: Optional Slack/Discord notifications

### **Rollback Strategy**

If issues occur:
1. **Vercel Dashboard**: Rollback to previous deployment
2. **Git Revert**: Revert problematic commits
3. **Environment Variables**: Quickly update configuration
4. **Domain Management**: Temporary domain switching if needed

## 🛠️ Maintenance & Updates

### **Regular Maintenance**

#### **Monthly Tasks**
- **Dependency Updates**: Update npm packages
- **Security Patches**: Apply security updates
- **Performance Review**: Monitor response times
- **Error Analysis**: Review error logs

#### **Quarterly Tasks**
- **BSP IPG Updates**: Check for BSP manual updates
- **SSL Certificate**: Verify certificate renewal
- **Domain Configuration**: Verify DNS settings
- **Backup Verification**: Test environment variable backup

### **Update Process**

1. **Development**: Test changes locally
2. **Staging**: Deploy to preview environment
3. **Testing**: Comprehensive testing on preview
4. **Production**: Deploy to production
5. **Verification**: Post-deployment verification

### **Emergency Procedures**

#### **Service Outage**
1. **Check Vercel Status**: https://vercel-status.com
2. **Review Logs**: Check function logs for errors
3. **Environment Check**: Verify environment variables
4. **BSP IPG Status**: Check BSP gateway status

#### **Security Incident**
1. **Rotate Credentials**: Update HMAC key if compromised
2. **Review Logs**: Check for suspicious activity
3. **Update Environment**: Deploy security patches
4. **Notify Stakeholders**: Inform relevant parties

## 📞 Support & Resources

### **Vercel Support**
- **Documentation**: https://vercel.com/docs
- **Community**: https://github.com/vercel/vercel/discussions
- **Support**: Vercel dashboard support chat

### **BSP IPG Support**
- **Technical Support**: BSP IPG technical team
- **Documentation**: BSP IPG Merchant Integration Manual v2.54
- **Portal**: BSP Merchant Portal

### **Project Resources**
- **Repository**: https://github.com/vikichand/donations.spcafiji.com
- **Issues**: GitHub Issues for bug reports
- **Documentation**: Project README and technical docs

## ✅ Deployment Checklist

### **Pre-Deployment**
- [ ] BSP IPG credentials obtained
- [ ] Environment variables prepared
- [ ] Domain DNS configured
- [ ] Local testing completed

### **Deployment**
- [ ] Vercel project created
- [ ] Repository connected
- [ ] Environment variables configured
- [ ] Custom domain added
- [ ] SSL certificate provisioned

### **Post-Deployment**
- [ ] Environment health check passed
- [ ] HMAC testing successful
- [ ] Payment form functional
- [ ] End-to-end testing completed
- [ ] Security checklist verified

### **Go-Live**
- [ ] BSP IPG integration approved
- [ ] Production testing completed
- [ ] Monitoring configured
- [ ] Documentation updated
- [ ] Stakeholders notified

**Deployment Status: Ready for Production** 🚀
