/**
 * BSP IPG Server Configuration
 * Loads credentials from environment variables
 */

require('dotenv').config();

class ServerConfig {
    constructor() {
        this.validateEnvironment();
    }

    // Get BSP IPG configuration with environment switching
    getBSPConfig() {
        const bspEnv = process.env.BSP_ENV || 'prod';

        // Base configuration (common to both environments)
        const config = {
            MERCHANT_NAME: process.env.MERCHANT_NAME,
            RETURN_URL: process.env.RETURN_URL,
            WEBSITE_ADDRESS: process.env.WEBSITE_ADDRESS
        };

        // Environment-specific configuration
        if (bspEnv === 'uat') {
            config.HMAC_KEY = process.env.BSP_HMAC_KEY_UAT || process.env.BSP_HMAC_KEY;
            config.MERCHANT_ID = process.env.MERCHANT_ID_UAT || process.env.MERCHANT_ID;
            config.MCC_CODE = process.env.MCC_CODE_UAT || process.env.MCC_CODE;
            config.BSP_PAYMENT_URL = process.env.BSP_PAYMENT_URL_UAT || process.env.BSP_PAYMENT_URL;
            config.BSP_PORTAL_URL = process.env.BSP_PORTAL_URL_UAT || process.env.BSP_PORTAL_URL;
        } else {
            config.HMAC_KEY = process.env.BSP_HMAC_KEY;
            config.MERCHANT_ID = process.env.MERCHANT_ID;
            config.MCC_CODE = process.env.MCC_CODE;
            config.BSP_PAYMENT_URL = process.env.BSP_PAYMENT_URL;
            config.BSP_PORTAL_URL = process.env.BSP_PORTAL_URL;
        }

        console.log(`[Config] Using BSP environment: ${bspEnv}`);
        console.log(`[Config] Payment URL: ${config.BSP_PAYMENT_URL}`);
        console.log(`[Config] Merchant ID: ${config.MERCHANT_ID ? config.MERCHANT_ID.substring(0, 4) + '***' + config.MERCHANT_ID.slice(-4) : 'NOT_SET'}`);
        console.log(`[Config] MCC Code: ${config.MCC_CODE || 'NOT_SET'}`);

        return config;
    }

    // Get public configuration (safe to send to client)
    getPublicConfig() {
        const bspEnv = process.env.BSP_ENV || 'prod';
        const environmentLabel = bspEnv === 'uat' ? 'UAT Testing' : 'Production';

        return {
            merchantName: process.env.MERCHANT_NAME,
            currency: '242', // FJD - BSP IPG protocol constant
            currencyName: 'FJD', // BSP IPG protocol constant
            environment: environmentLabel,
            bspEnvironment: bspEnv,
            returnUrl: process.env.RETURN_URL,
            websiteAddress: process.env.WEBSITE_ADDRESS,
            // BSP IPG Protocol Constants (BSP specification)
            merchantBankCode: '01',
            messageType: 'AR',
            version: '1.0',
            securityType: 'MERSECURE',
            defaultCardType: 'EX',
            // Business Configuration
            paymentDescription: process.env.BSP_PAYMENT_DESCRIPTION || 'SPCA Fiji Donation',
            defaultAmount: process.env.DEFAULT_AMOUNT || '1.00',
            minAmount: parseFloat(process.env.MIN_AMOUNT || '0.01'),
            maxAmount: parseFloat(process.env.MAX_AMOUNT || '999999.99'),
            decimalPlaces: parseInt(process.env.AMOUNT_DECIMAL_PLACES || '2')
        };
    }

    // Get server configuration
    getServerConfig() {
        return {
            port: process.env.PORT || 3000,
            nodeEnv: process.env.NODE_ENV || 'development',
            corsOrigin: process.env.CORS_ORIGIN || 'http://localhost:3000',
            rateLimitWindowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 900000,
            rateLimitMaxRequests: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100
        };
    }

    // Validate required environment variables
    validateEnvironment() {
        const required = [
            'BSP_HMAC_KEY',
            'MERCHANT_ID',
            'MCC_CODE',
            'MERCHANT_NAME',
            'BSP_PAYMENT_URL',
            'BSP_PORTAL_URL',
            'RETURN_URL',
            'WEBSITE_ADDRESS'
        ];

        const missing = required.filter(key => !process.env[key]);

        if (missing.length > 0) {
            throw new Error(`Missing required environment variables: ${missing.join(', ')}`);
        }

        // Validate HMAC key format (production key)
        if (!this.isValidHexString(process.env.BSP_HMAC_KEY)) {
            throw new Error('Invalid BSP_HMAC_KEY format - must be hex string');
        }

        // Validate UAT HMAC key format if provided
        if (process.env.BSP_HMAC_KEY_UAT && !this.isValidHexString(process.env.BSP_HMAC_KEY_UAT)) {
            throw new Error('Invalid BSP_HMAC_KEY_UAT format - must be hex string');
        }

        // Validate merchant ID format (BSP IPG specification: 15 characters)
        if (process.env.MERCHANT_ID.length !== 15) {
            throw new Error('Invalid MERCHANT_ID format - must be 15 characters');
        }

        // Validate UAT merchant ID format if provided
        if (process.env.MERCHANT_ID_UAT && process.env.MERCHANT_ID_UAT.length !== 15) {
            throw new Error('Invalid MERCHANT_ID_UAT format - must be 15 characters');
        }

        console.log('✅ Environment validation passed');
    }

    // Check if string is valid hex
    isValidHexString(str) {
        return /^[0-9a-fA-F]+$/.test(str) && str.length % 2 === 0;
    }

    // Check if URL is valid HTTPS
    isValidUrl(url) {
        try {
            const urlObj = new URL(url);
            return urlObj.protocol === 'https:';
        } catch {
            return false;
        }
    }
}

module.exports = new ServerConfig();
