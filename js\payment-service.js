/**
 * Payment Service for BSP IPG Integration
 * Handles payment processing and form submission to BSP IPG
 * Implements complete AR (Authorization Request) message flow
 */

class PaymentService {
    constructor() {
        this.debugService = null;
        this.hmacService = null;
        this.errorHandler = null;
        this.isProcessing = false;
    }

    /**
     * Initialize payment service
     */
    init(debugService, hmacService, errorHandler) {
        this.debugService = debugService;
        this.hmacService = hmacService;
        this.errorHandler = errorHandler;
        this.debugService.log('info', 'Payment service initialized');
    }

    /**
     * Process payment with BSP IPG using secure server API
     * @param {number} amount - Payment amount
     * @returns {Promise<void>}
     */
    async processPayment(amount) {
        if (this.isProcessing) {
            this.debugService.logWarning('Payment already in progress');
            return;
        }

        try {
            this.isProcessing = true;
            this.debugService.log('info', '=== STARTING SECURE PAYMENT PROCESS ===');

            // Ensure configuration is loaded
            await configService.ensureLoaded();

            // Validate configuration
            this.validateConfiguration();

            // Validate amount
            configService.validateAmount(amount);

            this.debugService.log('info', `Initiating secure payment for FJD ${amount.toFixed(2)}`);
            this.debugService.log('info', 'Requesting signed payment data from server...');

            // Get signed payment data from secure server
            const paymentResult = await this.initiateSecurePayment(amount);

            // Log payment request (no sensitive data)
            this.debugService.logPaymentRequest(paymentResult.formData);

            this.debugService.log('success', 'Secure payment data received from server');
            this.debugService.log('info', `Order number: ${paymentResult.orderNumber}`);
            this.debugService.log('info', 'HMAC checksum calculated server-side (secure)');

            // Populate and submit form
            this.populateForm(paymentResult.formData, paymentResult.bspPaymentUrl);
            this.submitForm();

        } catch (error) {
            this.debugService.logError(error, 'Secure payment processing failed');
            if (this.errorHandler) {
                this.errorHandler.handleError(error, 'Payment processing', false);
            }
            this.isProcessing = false;
            throw error;
        }
    }

    /**
     * Initiate payment through secure server API
     */
    async initiateSecurePayment(amount) {
        try {
            const response = await fetch(`${BSP_CONFIG.API_BASE_URL}/payment/initiate`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ amount })
            });

            if (!response.ok) {
                throw new Error(`Server request failed: ${response.status}`);
            }

            const result = await response.json();

            if (!result.success) {
                throw new Error(result.error || 'Payment initiation failed');
            }

            return result;

        } catch (error) {
            throw new Error(`Secure payment initiation failed: ${error.message}`);
        }
    }

    /**
     * NOTE: Payment data generation now handled server-side for security
     * This method is deprecated and kept for compatibility
     */
    generatePaymentData(amount) {
        // This method is no longer used - payment data is generated server-side
        // for security reasons. All sensitive credentials are kept on the server.
        throw new Error('Payment data generation moved to secure server-side API');
    }

    /**
     * NOTE: Transaction time formatting and order number generation
     * moved to server-side for security and consistency
     */

    /**
     * Validate BSP IPG configuration
     */
    validateConfiguration() {
        try {
            configService.validateConfig();
            this.debugService.logConfigValidation(true);
        } catch (error) {
            this.debugService.logConfigValidation(false, [error.message]);
            throw error;
        }
    }

    /**
     * Populate hidden form with secure payment data from server
     */
    populateForm(paymentData, bspPaymentUrl) {
        const form = document.getElementById('bspPaymentForm');
        if (!form) {
            throw new Error('Payment form not found');
        }

        // Set form action to BSP IPG URL (provided by server)
        form.action = bspPaymentUrl;

        // Populate all form fields
        Object.keys(paymentData).forEach(key => {
            const input = document.getElementById(key) || document.querySelector(`input[name="${key}"]`);
            if (input) {
                input.value = paymentData[key];
                this.debugService.log('info', `Set ${key}: ${this.sanitizeValue(key, paymentData[key])}`);
            } else {
                this.debugService.logWarning(`Form field not found: ${key}`);
            }
        });

        this.debugService.log('success', 'Secure payment form populated successfully');
        this.debugService.log('info', `Form action set to: ${bspPaymentUrl}`);
    }

    /**
     * Submit form to BSP IPG
     */
    submitForm() {
        const form = document.getElementById('bspPaymentForm');
        if (!form) {
            throw new Error('Payment form not found');
        }

        this.debugService.logFormSubmission(form.action);

        // Add a small delay to ensure debug message is visible
        setTimeout(() => {
            form.submit();
        }, 500);
    }

    /**
     * Handle payment response (called from thank you page)
     */
    handlePaymentResponse(responseParams) {
        this.debugService.log('info', '=== PAYMENT RESPONSE HANDLING ===');
        
        try {
            // Parse response parameters
            const responseData = this.parseResponseParams(responseParams);
            
            // Log response
            this.debugService.logResponse(responseData);
            
            // Verify checksum if present
            if (responseData.nar_checkSum) {
                this.verifyResponseChecksum(responseData);
            }
            
            return responseData;
            
        } catch (error) {
            this.debugService.logError(error, 'Response handling failed');
            throw error;
        }
    }

    /**
     * Parse response parameters from URL or form data
     */
    parseResponseParams(params) {
        const responseData = {};
        
        if (typeof params === 'string') {
            // Parse from URL query string
            const urlParams = new URLSearchParams(params);
            for (const [key, value] of urlParams) {
                responseData[key] = value;
            }
        } else if (typeof params === 'object') {
            // Use object directly
            Object.assign(responseData, params);
        }
        
        return responseData;
    }

    /**
     * Verify response checksum
     */
    async verifyResponseChecksum(responseData) {
        try {
            const isValid = await this.hmacService.verifyResponseChecksum(
                responseData, 
                responseData.nar_checkSum
            );
            
            if (isValid) {
                this.debugService.log('success', 'Response checksum verification PASSED');
            } else {
                this.debugService.log('error', 'Response checksum verification FAILED');
            }
            
            return isValid;
            
        } catch (error) {
            this.debugService.logError(error, 'Checksum verification error');
            return false;
        }
    }

    /**
     * Sanitize sensitive values for logging
     */
    sanitizeValue(key, value) {
        const sensitiveFields = ['nar_checkSum', 'nar_remitterEmail', 'nar_remitterMobile'];
        
        if (sensitiveFields.includes(key)) {
            if (key === 'nar_checkSum') {
                return `${value.substring(0, 8)}...${value.substring(value.length - 8)}`;
            }
            return '[REDACTED]';
        }
        
        return value;
    }

    /**
     * Get payment status from response
     */
    getPaymentStatus(responseData) {
        const remarks = responseData.nar_remarks;
        const authCode = responseData.nar_debitAuthCode;
        
        if (remarks === 'Approved' && authCode === '00') {
            return 'SUCCESS';
        } else if (remarks) {
            return 'FAILED';
        } else {
            return 'UNKNOWN';
        }
    }

    /**
     * Reset processing state
     */
    resetProcessing() {
        this.isProcessing = false;
    }

    /**
     * Get service status
     */
    getStatus() {
        return {
            initialized: !!(this.debugService && this.hmacService),
            processing: this.isProcessing,
            configValid: this.isConfigurationValid()
        };
    }

    /**
     * Check if configuration is valid
     */
    isConfigurationValid() {
        try {
            configService.validateConfig();
            return true;
        } catch {
            return false;
        }
    }
}

// Create global instance
window.paymentService = new PaymentService();
