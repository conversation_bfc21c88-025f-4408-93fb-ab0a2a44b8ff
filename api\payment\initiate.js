/**
 * POST /api/payment/initiate
 * Initiates BSP IPG payment processing
 */

const { body, validationResult } = require('express-validator');
const config = require('../../server/config/config');
const paymentService = require('../../server/services/payment-service');

// Get validation limits from configuration
const getValidationLimits = () => {
    const publicConfig = config.getPublicConfig();
    return {
        minAmount: publicConfig.minAmount,
        maxAmount: publicConfig.maxAmount,
        decimalPlaces: publicConfig.decimalPlaces
    };
};

// Validation middleware
const validateAmount = [
    body('amount')
        .custom((value) => {
            const limits = getValidationLimits();
            const numAmount = parseFloat(value);

            if (!value || isNaN(numAmount)) {
                throw new Error('Amount must be a valid number');
            }

            if (numAmount <= 0) {
                throw new Error('Amount must be greater than zero');
            }

            if (numAmount < limits.minAmount || numAmount > limits.maxAmount) {
                throw new Error(`Amount must be between FJD ${limits.minAmount} and FJD ${limits.maxAmount.toLocaleString()}`);
            }

            const decimalPlaces = (numAmount.toString().split('.')[1] || '').length;
            if (decimalPlaces > limits.decimalPlaces) {
                throw new Error(`Amount cannot have more than ${limits.decimalPlaces} decimal places`);
            }

            const totalDigits = numAmount.toString().replace('.', '').length;
            if (totalDigits > 16) {
                throw new Error('Amount exceeds BSP IPG maximum precision (16 digits)');
            }

            return true;
        })
];

module.exports = async (req, res) => {
    // Set CORS headers
    res.setHeader('Access-Control-Allow-Credentials', true);
    res.setHeader('Access-Control-Allow-Origin', process.env.CORS_ORIGIN || '*');
    res.setHeader('Access-Control-Allow-Methods', 'POST,OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

    // Set referrer policy for BSP IPG
    res.setHeader('Referrer-Policy', 'unsafe-url');

    // Store request for logging purposes
    global.currentRequest = req;

    // Handle preflight requests
    if (req.method === 'OPTIONS') {
        res.status(200).end();
        return;
    }

    // Only allow POST requests
    if (req.method !== 'POST') {
        return res.status(405).json({
            success: false,
            error: 'Method not allowed'
        });
    }

    try {
        // Manual validation using centralized config
        const { amount } = req.body;
        const limits = getValidationLimits();
        const numAmount = parseFloat(amount);

        if (!amount || isNaN(numAmount)) {
            return res.status(400).json({
                success: false,
                error: 'Amount must be a valid number'
            });
        }

        // BSP IPG requires amount > 0
        if (numAmount <= 0) {
            return res.status(400).json({
                success: false,
                error: 'Amount must be greater than zero'
            });
        }

        if (numAmount < limits.minAmount || numAmount > limits.maxAmount) {
            return res.status(400).json({
                success: false,
                error: `Amount must be between FJD ${limits.minAmount} and FJD ${limits.maxAmount.toLocaleString()}`
            });
        }

        const decimalPlaces = (numAmount.toString().split('.')[1] || '').length;
        if (decimalPlaces > limits.decimalPlaces) {
            return res.status(400).json({
                success: false,
                error: `Amount cannot have more than ${limits.decimalPlaces} decimal places`
            });
        }

        // BSP IPG total length validation (16 digits including decimals)
        const totalDigits = numAmount.toString().replace('.', '').length;
        if (totalDigits > 16) {
            return res.status(400).json({
                success: false,
                error: 'Amount exceeds BSP IPG maximum precision (16 digits)'
            });
        }
        
        console.log(`[API] Payment initiation requested for FJD ${amount}`);
        
        // Process payment
        const result = await paymentService.initiatePayment(amount);
        
        res.status(200).json(result);
        
    } catch (error) {
        console.error('[API] Payment initiation failed:', error.message);
        res.status(400).json({
            success: false,
            error: error.message
        });
    }
};
