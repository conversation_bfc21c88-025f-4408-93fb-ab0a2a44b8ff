/**
 * Secure BSP IPG Payment Server
 * Provides secure server-side payment processing for SPCA Fiji donations
 * Handles all sensitive credentials and HMAC calculations server-side
 */

const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const path = require('path');

// Load configuration
const config = require('./config/config');
const apiRoutes = require('./routes/api');

const app = express();
const serverConfig = config.getServerConfig();

// Security middleware
app.use(helmet({
    contentSecurityPolicy: {
        directives: {
            defaultSrc: ["'self'"],
            styleSrc: ["'self'", "'unsafe-inline'"],
            scriptSrc: ["'self'"],
            imgSrc: ["'self'", "data:", "https:"],
            connectSrc: ["'self'", "https://oceania.thecardservicesonline.com"],
            formAction: ["'self'", "https://oceania.thecardservicesonline.com"]
        }
    }
}));

// CORS configuration
app.use(cors({
    origin: serverConfig.corsOrigin,
    credentials: true,
    methods: ['GET', 'POST'],
    allowedHeaders: ['Content-Type', 'Authorization']
}));

// Rate limiting
const limiter = rateLimit({
    windowMs: serverConfig.rateLimitWindowMs, // 15 minutes
    max: serverConfig.rateLimitMaxRequests, // limit each IP to 100 requests per windowMs
    message: {
        error: 'Too many requests from this IP, please try again later.'
    },
    standardHeaders: true,
    legacyHeaders: false
});

app.use('/api/', limiter);

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Serve static files (client-side application)
app.use(express.static(path.join(__dirname, '../')));

// API routes
app.use('/api', apiRoutes);

// Serve main application
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, '../index.html'));
});

// Serve thank you page
app.get('/thank-you', (req, res) => {
    res.sendFile(path.join(__dirname, '../thank-you.html'));
});

// Serve HMAC test page (development only)
app.get('/test-hmac', (req, res) => {
    if (serverConfig.nodeEnv === 'production') {
        return res.status(404).send('Not found');
    }
    res.sendFile(path.join(__dirname, '../test-hmac.html'));
});

// Error handling middleware
app.use((err, req, res, next) => {
    console.error('Server error:', err);
    
    res.status(err.status || 500).json({
        success: false,
        error: serverConfig.nodeEnv === 'production' 
            ? 'Internal server error' 
            : err.message
    });
});

// 404 handler
app.use((req, res) => {
    res.status(404).json({
        success: false,
        error: 'Endpoint not found'
    });
});

// Start server
const PORT = serverConfig.port;

app.listen(PORT, () => {
    console.log('🚀 BSP IPG Payment Server Started');
    console.log(`📍 Server running on port ${PORT}`);
    console.log(`🌍 Environment: ${serverConfig.nodeEnv}`);
    console.log(`🔒 CORS origin: ${serverConfig.corsOrigin}`);
    console.log(`⚡ Rate limit: ${serverConfig.rateLimitMaxRequests} requests per ${serverConfig.rateLimitWindowMs/60000} minutes`);
    
    if (serverConfig.nodeEnv === 'development') {
        console.log('🧪 HMAC testing endpoint available at /api/hmac/test');
    }
    
    console.log('✅ Server ready to accept connections');
});

// Graceful shutdown
process.on('SIGTERM', () => {
    console.log('SIGTERM received, shutting down gracefully');
    process.exit(0);
});

process.on('SIGINT', () => {
    console.log('SIGINT received, shutting down gracefully');
    process.exit(0);
});

module.exports = app;
