/**
 * Vercel Serverless Function: POST /api/payment/verify
 * Verifies payment response from BSP IPG
 */

const config = require('../../server/config/config');
const paymentService = require('../../server/services/payment-service');

module.exports = async (req, res) => {
    // Set CORS headers
    res.setHeader('Access-Control-Allow-Credentials', true);
    res.setHeader('Access-Control-Allow-Origin', process.env.CORS_ORIGIN || '*');
    res.setHeader('Access-Control-Allow-Methods', 'POST,OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

    // Handle preflight requests
    if (req.method === 'OPTIONS') {
        res.status(200).end();
        return;
    }

    // Only allow POST requests
    if (req.method !== 'POST') {
        return res.status(405).json({
            success: false,
            error: 'Method not allowed'
        });
    }

    try {
        // Basic validation for required fields
        const { nar_orderNo, nar_msgType } = req.body;
        
        if (!nar_orderNo || !nar_msgType) {
            return res.status(400).json({
                success: false,
                error: 'Order number and message type are required'
            });
        }

        const responseData = req.body;
        
        console.log(`[API] Payment verification requested for order: ${responseData.nar_orderNo}`);
        
        // Verify payment response
        const result = paymentService.verifyPaymentResponse(responseData);
        
        res.status(200).json(result);
        
    } catch (error) {
        console.error('[API] Payment verification failed:', error.message);
        res.status(400).json({
            success: false,
            error: error.message
        });
    }
};
