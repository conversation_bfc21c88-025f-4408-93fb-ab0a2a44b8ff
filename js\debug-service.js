/**
 * Debug Service for BSP IPG Payment Testing
 * Provides real-time debugging console for payment flow
 * Shows payment requests, responses, errors, and transaction flow
 */

class DebugService {
    constructor() {
        this.console = null;
        this.maxMessages = BSP_CONFIG?.MAX_DEBUG_MESSAGES || 100;
        this.messageCount = 0;
    }

    /**
     * Initialize debug service with console element
     */
    init(consoleElement) {
        this.console = consoleElement;
        this.log('info', 'Debug service initialized');
        this.log('info', `Max messages: ${this.maxMessages}`);
    }

    /**
     * Log a message to the debug console
     * @param {string} level - Log level (info, success, warning, error)
     * @param {string} message - Message to log
     * @param {Object} data - Optional data object to display
     */
    log(level, message, data = null) {
        if (!this.console) return;

        const timestamp = new Date().toLocaleTimeString();
        const messageElement = document.createElement('div');
        messageElement.className = 'debug-message';

        let displayMessage = message;
        
        // Add data if provided (but filter sensitive information)
        if (data) {
            const sanitizedData = this.sanitizeData(data);
            displayMessage += ` | Data: ${JSON.stringify(sanitizedData, null, 2)}`;
        }

        messageElement.innerHTML = `
            <span class="timestamp">[${timestamp}]</span>
            <span class="message ${level}">${this.escapeHtml(displayMessage)}</span>
        `;

        this.console.appendChild(messageElement);
        
        // Auto-scroll to bottom
        this.console.scrollTop = this.console.scrollHeight;

        // Limit number of messages
        this.messageCount++;
        this.limitMessages();

        // Also log to browser console for debugging
        console.log(`[BSP IPG] ${level.toUpperCase()}: ${message}`, data);
    }

    /**
     * Log payment request data
     */
    logPaymentRequest(paymentData) {
        this.log('info', '=== PAYMENT REQUEST INITIATED ===');
        this.log('info', 'Constructing BSP IPG Authorization Request (AR)');
        
        // Log non-sensitive payment data
        const sanitizedData = {
            msgType: paymentData.nar_msgType,
            orderNo: paymentData.nar_orderNo,
            amount: paymentData.nar_txnAmount,
            currency: paymentData.nar_txnCurrency,
            merchantId: paymentData.nar_merId,
            transactionTime: paymentData.nar_merTxnTime,
            paymentDesc: paymentData.nar_paymentDesc,
            returnUrl: paymentData.nar_returnUrl,
            mccCode: paymentData.nar_mcccode,
            version: paymentData.nar_version,
            secureType: paymentData.nar_Secure
        };

        this.log('info', 'Payment request data prepared', sanitizedData);
    }

    /**
     * Log checksum calculation process
     */
    logChecksumCalculation(sourceString, checksumLength) {
        this.log('info', '=== CHECKSUM CALCULATION ===');
        this.log('info', 'Using HMAC SHA-256 algorithm');
        this.log('info', `Source string length: ${sourceString.length} characters`);
        this.log('info', `Source string preview: ${sourceString.substring(0, 100)}...`);
        this.log('success', `Checksum calculated (${checksumLength} chars)`);
    }

    /**
     * Log form submission
     */
    logFormSubmission(targetUrl) {
        this.log('info', '=== FORM SUBMISSION ===');
        this.log('info', `Submitting to BSP IPG: ${targetUrl}`);
        this.log('warning', 'Redirecting to BSP payment gateway...');
        this.log('info', 'User will complete payment on BSP secure page');
        this.log('info', 'Please complete payment and return to see response');
    }

    /**
     * Log response handling
     */
    logResponse(responseData) {
        this.log('info', '=== PAYMENT RESPONSE RECEIVED ===');
        
        const sanitizedResponse = {
            msgType: responseData.nar_msgType,
            orderNo: responseData.nar_orderNo,
            transactionId: responseData.nar_narTxnId,
            status: responseData.nar_remarks,
            amount: responseData.nar_txnAmount,
            authCode: responseData.nar_debitAuthCode,
            cardType: responseData.nar_cardType,
            remitterName: responseData.nar_remitterName
        };

        this.log('info', 'Response data received', sanitizedResponse);
        
        // Log status with appropriate level
        const status = responseData.nar_remarks;
        const authCode = responseData.nar_debitAuthCode;
        
        if (status === 'Approved' && authCode === '00') {
            this.log('success', 'Payment APPROVED - Transaction successful');
        } else if (status) {
            this.log('error', `Payment ${status} - Code: ${authCode || 'N/A'}`);
        } else {
            this.log('warning', 'Payment status unclear - check response data');
        }
    }

    /**
     * Log errors with context
     */
    logError(error, context = '') {
        const errorMessage = error?.message || error || 'Unknown error';
        this.log('error', `${context ? context + ': ' : ''}${errorMessage}`);
        
        if (error?.stack && BSP_CONFIG?.DEBUG_MODE) {
            console.error('[BSP IPG Error Stack]', error);
        }
    }

    /**
     * Log warnings
     */
    logWarning(message) {
        this.log('warning', message);
    }

    /**
     * Log configuration validation
     */
    logConfigValidation(isValid, missingFields = []) {
        if (isValid) {
            this.log('success', 'BSP IPG configuration validated successfully');
        } else {
            this.log('error', `Configuration validation failed. Missing: ${missingFields.join(', ')}`);
        }
    }

    /**
     * Log application initialization
     */
    logAppInitialization() {
        this.log('info', 'BSP IPG Payment Testing Application starting...');
        
        const envInfo = configService?.getEnvironmentInfo();
        if (envInfo) {
            Object.entries(envInfo).forEach(([key, value]) => {
                this.log('info', `${key}: ${value}`);
            });
        }
    }

    /**
     * Clear the debug console
     */
    clear() {
        if (this.console) {
            this.console.innerHTML = '';
            this.messageCount = 0;
            this.log('info', 'Debug console cleared');
        }
    }

    /**
     * Sanitize data to remove sensitive information
     */
    sanitizeData(data) {
        if (!data || typeof data !== 'object') return data;
        
        const sanitized = { ...data };
        
        // Remove or mask sensitive fields
        const sensitiveFields = [
            'nar_checkSum', 'checkSum', 'hmacKey', 'HMAC_KEY', 'password',
            'nar_remitterEmail', 'nar_remitterMobile', 'nar_cardNo'
        ];

        sensitiveFields.forEach(field => {
            if (sanitized[field]) {
                if (field === 'nar_checkSum' || field === 'checkSum') {
                    // Show only first and last few characters of checksum
                    const value = sanitized[field];
                    sanitized[field] = `${value.substring(0, 8)}...${value.substring(value.length - 8)}`;
                } else if (field === 'nar_cardNo') {
                    // Mask card number
                    sanitized[field] = sanitized[field].replace(/\d(?=\d{4})/g, 'X');
                } else {
                    sanitized[field] = '[REDACTED]';
                }
            }
        });

        return sanitized;
    }

    /**
     * Escape HTML to prevent XSS
     */
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    /**
     * Limit number of messages in console
     */
    limitMessages() {
        if (!this.console) return;

        const messages = this.console.querySelectorAll('.debug-message');
        if (messages.length > this.maxMessages) {
            const excess = messages.length - this.maxMessages;
            for (let i = 0; i < excess; i++) {
                messages[i].remove();
            }
            this.messageCount = this.maxMessages;
        }
    }

    /**
     * Export console content for debugging
     */
    exportConsole() {
        if (!this.console) return '';

        const messages = this.console.querySelectorAll('.debug-message');
        return Array.from(messages)
            .map(msg => msg.textContent)
            .join('\n');
    }

    /**
     * Get debug service status
     */
    getStatus() {
        return {
            initialized: !!this.console,
            messageCount: this.messageCount,
            maxMessages: this.maxMessages
        };
    }
}

// Create global instance
window.debugService = new DebugService();
