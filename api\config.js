/**
 * Vercel Serverless Function: GET /api/config
 * Returns public configuration (no sensitive data)
 */

const config = require('../server/config/config');

module.exports = async (req, res) => {
    // Set CORS headers
    res.setHeader('Access-Control-Allow-Credentials', true);
    res.setHeader('Access-Control-Allow-Origin', process.env.CORS_ORIGIN || '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET,OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

    // Handle preflight requests
    if (req.method === 'OPTIONS') {
        res.status(200).end();
        return;
    }

    // Only allow GET requests
    if (req.method !== 'GET') {
        return res.status(405).json({
            success: false,
            error: 'Method not allowed'
        });
    }

    try {
        const publicConfig = config.getPublicConfig();
        
        console.log('[API] Public configuration requested');
        
        res.status(200).json({
            success: true,
            config: publicConfig
        });
        
    } catch (error) {
        console.error('[API] Config request failed:', error.message);
        res.status(500).json({
            success: false,
            error: 'Configuration not available'
        });
    }
};
